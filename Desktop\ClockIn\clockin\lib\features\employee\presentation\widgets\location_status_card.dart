import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/location_service.dart';
import '../providers/time_tracking_provider.dart';

class LocationStatusCard extends ConsumerWidget {
  const LocationStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeTrackingState = ref.watch(timeTrackingProvider);
    final canClockIn = timeTrackingState.canClockIn;
    final nearbyWorksites = timeTrackingState.nearbyWorksites;
    final selectedWorksite = timeTrackingState.selectedWorksite;
    final currentLocation = timeTrackingState.currentLocation;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: canClockIn
                ? [
                    AppTheme.accentColor.withOpacity(0.1),
                    AppTheme.accentColor.withOpacity(0.05),
                  ]
                : [
                    AppTheme.warningColor.withOpacity(0.1),
                    AppTheme.warningColor.withOpacity(0.05),
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: canClockIn
                        ? AppTheme.accentColor
                        : AppTheme.warningColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    canClockIn
                        ? FontAwesomeIcons.locationDot
                        : FontAwesomeIcons.locationCrosshairs,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Location Status',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        canClockIn ? 'At Worksite' : 'Away from Worksite',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status Indicator with Pulse Animation
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: canClockIn
                        ? AppTheme.accentColor
                        : AppTheme.warningColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: (canClockIn
                                ? AppTheme.accentColor
                                : AppTheme.warningColor)
                            .withOpacity(0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),

            // Location Information
            if (selectedWorksite != null) ...[
              _buildWorksiteInfo(selectedWorksite, currentLocation),
            ] else if (nearbyWorksites.isNotEmpty) ...[
              _buildNearbyWorksites(nearbyWorksites, ref),
            ] else ...[
              _buildNoWorksites(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWorksiteInfo(worksite, currentLocation) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FontAwesomeIcons.building,
              size: 16,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                worksite.name,
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.accentColor.withOpacity(0.3),
                ),
              ),
              child: Text(
                'ACTIVE',
                style: TextStyle(
                  fontSize: 10,
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        if (worksite.address != null)
          Row(
            children: [
              Icon(
                FontAwesomeIcons.locationDot,
                size: 16,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  worksite.address!,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Icon(
              FontAwesomeIcons.bullseye,
              size: 16,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 12),
            Text(
              'Within ${worksite.radius.round()}m radius',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
            ),
            const Spacer(),
            if (currentLocation != null)
              FutureBuilder<double>(
                future: Future.value(worksite.distanceFrom(
                  currentLocation.latitude,
                  currentLocation.longitude,
                )),
                builder: (context, snapshot) {
                  final distance = snapshot.data ?? 0.0;
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      LocationService().formatDistance(distance),
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.accentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildNearbyWorksites(List nearbyWorksites, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FontAwesomeIcons.mapLocationDot,
              size: 16,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 12),
            Text(
              'Nearby Worksites',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        ...nearbyWorksites.take(3).map((worksite) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.warningColor.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.building,
                  size: 14,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        worksite.name,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (worksite.description.isNotEmpty)
                        Text(
                          worksite.description,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textLight,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    return FutureBuilder<double>(
                      future: ref.read(locationServiceProvider).distanceToWorksite(worksite),
                      builder: (context, snapshot) {
                        final distance = snapshot.data ?? 0.0;
                        final isNear = distance <= worksite.radius;
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: isNear
                                ? AppTheme.accentColor.withOpacity(0.1)
                                : AppTheme.warningColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            LocationService().formatDistance(distance),
                            style: TextStyle(
                              fontSize: 12,
                              color: isNear ? AppTheme.accentColor : AppTheme.warningColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        )).toList(),
        
        if (nearbyWorksites.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              '+${nearbyWorksites.length - 3} more worksites nearby',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textLight,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNoWorksites() {
    return Column(
      children: [
        Icon(
          FontAwesomeIcons.mapLocationDot,
          size: 48,
          color: AppTheme.textLight,
        ),
        const SizedBox(height: 16),
        Text(
          'No worksites found nearby',
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Make sure you are within range of an assigned worksite to start working',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textLight,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () {
            // TODO: Refresh location and worksites
          },
          icon: const Icon(FontAwesomeIcons.arrowsRotate, size: 16),
          label: const Text('Refresh Location'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.secondaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ),
      ],
    );
  }
}
