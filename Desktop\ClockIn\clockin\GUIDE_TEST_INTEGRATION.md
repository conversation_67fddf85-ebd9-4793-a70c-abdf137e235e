# Guide de Test d'Intégration - ClockIn

## 🚀 Démarrage du Backend

### 1. <PERSON><PERSON><PERSON>rer le Serveur Laravel

```bash
cd C:\wamp64\www\clockin
start_clockin.bat
```

Le script va :
- ✅ Trouver un port disponible (8080-8085)
- ✅ Configurer automatiquement l'URL
- ✅ Générer la documentation
- ✅ Démarrer le serveur Laravel
- ✅ Ouvrir la documentation dans le navigateur

### 2. Vérifier le Démarrage

Le serveur sera accessible sur :
- **API Base** : `http://localhost:PORT/api`
- **Documentation** : `http://localhost:PORT/docs`
- **Application** : `http://localhost:PORT`

## 🧪 Test de l'Intégration Frontend

### 1. Lancer l'Application Flutter

```bash
cd Desktop\ClockIn\clockin
flutter run
```

### 2. Tester la Connectivité API

1. **Ouvrir l'application Flutter**
2. **Sur l'écran de connexion**, cliquer sur **"Test API Connection"**
3. **Exécuter les tests** :
   - Test API Health (peut échouer - normal)
   - Test Public Endpoints
   - Run All Tests

### 3. Résultats Attendus

#### Tests Publics ✅
- **login** : ✅ Doit répondre (même avec erreur d'authentification)

#### Tests Authentifiés ❌ (Sans Token)
- **me** : ❌ Erreur 401 (normal sans authentification)
- **my-sites** : ❌ Erreur 401 (normal)
- **check-location** : ❌ Erreur 401 (normal)

## 🔐 Test d'Authentification

### Comptes de Test Disponibles

D'après le script de démarrage :
- **Admin** : `<EMAIL>` / `password123`
- **Employé** : `<EMAIL>` / `password123`

### Test de Connexion

1. **Utiliser l'écran de connexion Flutter**
2. **Saisir les identifiants** :
   ```
   Email: <EMAIL>
   Mot de passe: password123
   ```
3. **Cliquer sur "Se connecter"**

### Résultat Attendu

Si l'intégration fonctionne :
- ✅ Connexion réussie
- ✅ Redirection vers le dashboard
- ✅ Token stocké localement
- ✅ Utilisateur authentifié

## 📱 Test des Fonctionnalités

### 1. Test du Pointage

Une fois connecté :
1. **Vérifier la localisation** (peut nécessiter des permissions)
2. **Voir les sites assignés** (via `getMySites()`)
3. **Tenter un pointage** (si dans la zone)

### 2. Test de Vérification

1. **Demander une vérification de localisation**
2. **Vérifier la réponse du serveur**

## 🔧 Configuration Frontend

### Mise à Jour de l'URL API

Si le serveur utilise un port différent de 8081, mettre à jour :

**Fichier** : `lib/core/constants/app_constants.dart`
```dart
static const String baseUrl = 'http://localhost:PORT/api';
```

Remplacer `PORT` par le port affiché par le script de démarrage.

## 🐛 Dépannage

### Problème : API Non Accessible

**Vérifications** :
1. ✅ Le serveur Laravel est-il démarré ?
2. ✅ Le port est-il correct dans `app_constants.dart` ?
3. ✅ Y a-t-il des erreurs dans les logs Laravel ?

**Solutions** :
```bash
# Vérifier le serveur
curl http://localhost:PORT/api/login

# Redémarrer le serveur
cd C:\wamp64\www\clockin
start_clockin.bat
```

### Problème : Erreur d'Authentification

**Vérifications** :
1. ✅ Les identifiants sont-ils corrects ?
2. ✅ La base de données est-elle configurée ?
3. ✅ Les utilisateurs de test existent-ils ?

**Solutions** :
```bash
# Vérifier la base de données
php artisan migrate:status

# Créer les utilisateurs de test
php artisan db:seed
```

### Problème : Erreur de CORS

Si vous voyez des erreurs CORS dans la console :

**Solution** : Vérifier la configuration CORS dans Laravel
```bash
# Publier la configuration CORS
php artisan vendor:publish --tag="cors"
```

## 📊 Tests Manuels avec Postman

Le backend inclut une collection Postman :
- **Fichier** : `ClockIn_API.postman_collection.json`
- **Import** dans Postman pour tester manuellement

### Tests Recommandés

1. **POST** `/api/login` - Test d'authentification
2. **GET** `/api/me` - Profil utilisateur (avec token)
3. **GET** `/api/my-sites` - Sites assignés
4. **POST** `/api/check-location` - Vérification localisation

## 📝 Logs et Débogage

### Logs Laravel
```bash
# Voir les logs en temps réel
tail -f storage/logs/laravel.log
```

### Logs Flutter
```bash
# Logs Flutter en temps réel
flutter logs
```

### Debug Console
- Tous les appels API sont loggés en mode debug
- Utiliser les outils de développement pour voir les requêtes réseau

## ✅ Checklist de Validation

### Backend
- [ ] Serveur Laravel démarré sur le bon port
- [ ] Documentation accessible
- [ ] Base de données configurée
- [ ] Utilisateurs de test créés

### Frontend
- [ ] Application Flutter compilée sans erreur
- [ ] URL API correcte dans les constantes
- [ ] Test de connectivité réussi
- [ ] Authentification fonctionnelle

### Intégration
- [ ] Login avec comptes de test réussi
- [ ] Réponses API correctement parsées
- [ ] Messages multilingues affichés
- [ ] Navigation post-authentification

## 🎯 Prochaines Étapes

1. **Valider l'authentification** avec les comptes de test
2. **Tester le pointage** avec des sites réels
3. **Implémenter l'interface utilisateur** pour les nouvelles fonctionnalités
4. **Ajouter la gestion d'erreurs** spécifique au backend
5. **Optimiser les performances** et la mise en cache

## 📞 Support

En cas de problème :
1. **Vérifier les logs** Laravel et Flutter
2. **Utiliser l'écran de test API** pour diagnostiquer
3. **Tester manuellement** avec Postman
4. **Consulter la documentation** générée automatiquement

L'intégration est maintenant prête pour les tests ! 🚀
