class AppConstants {
  // App Information
  static const String appName = 'ClockIn';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:8081/api'; // Local development server
  // static const String baseUrl = 'http://********:8081/api'; // Android Emulator
  // static const String baseUrl = 'http://127.0.0.1:8081/api'; // iOS Simulator
  // static const String baseUrl = 'http://YOUR_IP:8081/api'; // Physical Device
  
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'selected_language';
  static const String fcmTokenKey = 'fcm_token';
  
  // Location Settings
  static const double defaultWorksiteRadius = 50.0; // meters
  static const int locationUpdateInterval = 5; // minutes
  static const double minLocationAccuracy = 100.0; // meters
  
  // Security Settings
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const int passwordMinLength = 6;
  
  // UI Constants
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Time Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm:ss';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  
  // Validation
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phoneRegex = r'^\+?[1-9]\d{1,14}$';
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'An unknown error occurred';
  static const String locationError = 'Location access denied';
  static const String permissionError = 'Permission denied';
  
  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String logoutSuccess = 'Logout successful';
  static const String clockInSuccess = 'Clocked in successfully';
  static const String clockOutSuccess = 'Clocked out successfully';
  
  // Hive Box Names
  static const String userBox = 'user_box';
  static const String timeEntryBox = 'time_entry_box';
  static const String worksiteBox = 'worksite_box';
  static const String settingsBox = 'settings_box';
  
  // Firebase
  static const String fcmTopicAll = 'all_users';
  static const String fcmTopicEmployees = 'employees';
  static const String fcmTopicAdmins = 'admins';
  
  // Notification Types
  static const String notificationTypeLocationVerification = 'location_verification';
  static const String notificationTypeSiteAssignment = 'site_assignment';
  static const String notificationTypeGeneral = 'general';
  
  // Security Violation Types
  static const String violationMockLocation = 'mock_location';
  static const String violationRootedDevice = 'rooted_device';
  static const String violationLocationOutsideRadius = 'location_outside_radius';
  static const String violationMultipleDevices = 'multiple_devices';
  
  // Work Time Limits
  static const Duration maxWorkDayDuration = Duration(hours: 12);
  static const Duration standardWorkDayDuration = Duration(hours: 8);
  static const Duration maxBreakDuration = Duration(hours: 2);
  
  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
}
