import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../utils/message_utils.dart';
import 'api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  UserModel? _currentUser;
  String? _authToken;

  UserModel? get currentUser => _currentUser;
  String? get authToken => _authToken;
  bool get isAuthenticated => _currentUser != null && _authToken != null;

  Future<void> initialize() async {
    _apiService.initialize();
    await _loadStoredAuth();
  }

  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load token
      _authToken = prefs.getString(AppConstants.userTokenKey);
      
      // Load user data
      final userJson = prefs.getString(AppConstants.userDataKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = UserModel.fromJson(userData);
      }

      // Set token in API service
      if (_authToken != null) {
        await _apiService.setAuthToken(_authToken!);
      }
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
      await _clearStoredAuth();
    }
  }

  Future<AuthResult> login(String email, String password) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('Email and password are required');
      }

      if (!_isValidEmail(email)) {
        return AuthResult.failure('Please enter a valid email address');
      }

      // Prepare login data (send password as plain text - backend will handle hashing)
      final loginData = {
        'email': email.toLowerCase().trim(),
        'password': password,
        'device_info': await _getDeviceInfo(),
      };

      // Make API call
      final response = await _apiService.post('/login', loginData);

      if (MessageUtils.isSuccessResponse(response)) {
        final data = response['data'] as Map<String, dynamic>;

        // Extract token and user data
        _authToken = data['token'] as String;
        _currentUser = UserModel.fromJson(data['user'] as Map<String, dynamic>);

        // Store auth data
        await _storeAuthData();

        // Set token in API service
        await _apiService.setAuthToken(_authToken!);

        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure(MessageUtils.getErrorMessage(response));
      }
    } catch (e) {
      debugPrint('Login error: $e');
      return AuthResult.failure(_getErrorMessage(e));
    }
  }

  Future<AuthResult> logout() async {
    try {
      // Call logout API if authenticated
      if (isAuthenticated) {
        try {
          await _apiService.post('/logout', {});
        } catch (e) {
          // Continue with logout even if API call fails
          debugPrint('Logout API error: $e');
        }
      }

      // Clear local data
      await _clearAuth();

      return AuthResult.success(null);
    } catch (e) {
      debugPrint('Logout error: $e');
      return AuthResult.failure('Logout failed');
    }
  }

  Future<bool> validateToken() async {
    if (!isAuthenticated) return false;

    try {
      final response = await _apiService.get('/me');
      
      if (response['success'] == true) {
        // Update user data
        _currentUser = UserModel.fromJson(response['data'] as Map<String, dynamic>);
        await _storeUserData();
        return true;
      } else {
        await _clearAuth();
        return false;
      }
    } catch (e) {
      debugPrint('Token validation error: $e');
      await _clearAuth();
      return false;
    }
  }

  Future<AuthResult> refreshToken() async {
    if (_authToken == null) {
      return AuthResult.failure('No token to refresh');
    }

    try {
      final response = await _apiService.post('/refresh', {});
      
      if (response['success'] == true) {
        final data = response['data'] as Map<String, dynamic>;
        _authToken = data['token'] as String;
        
        await _storeAuthToken();
        await _apiService.setAuthToken(_authToken!);
        
        return AuthResult.success(_currentUser);
      } else {
        await _clearAuth();
        return AuthResult.failure('Token refresh failed');
      }
    } catch (e) {
      debugPrint('Token refresh error: $e');
      await _clearAuth();
      return AuthResult.failure('Token refresh failed');
    }
  }

  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    if (!isAuthenticated) {
      return AuthResult.failure('User not authenticated');
    }

    try {
      if (newPassword.length < AppConstants.passwordMinLength) {
        return AuthResult.failure('Password must be at least ${AppConstants.passwordMinLength} characters');
      }

      final data = {
        'current_password': _hashPassword(currentPassword),
        'new_password': _hashPassword(newPassword),
        'new_password_confirmation': _hashPassword(newPassword),
      };

      final response = await _apiService.post('/change-password', data);
      
      if (response['success'] == true) {
        return AuthResult.success(_currentUser);
      } else {
        return AuthResult.failure(response['message'] ?? 'Password change failed');
      }
    } catch (e) {
      debugPrint('Change password error: $e');
      return AuthResult.failure(_getErrorMessage(e));
    }
  }

  Future<void> _storeAuthData() async {
    await _storeAuthToken();
    await _storeUserData();
  }

  Future<void> _storeAuthToken() async {
    if (_authToken != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userTokenKey, _authToken!);
    }
  }

  Future<void> _storeUserData() async {
    if (_currentUser != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
    }
  }

  Future<void> _clearAuth() async {
    _currentUser = null;
    _authToken = null;
    await _apiService.clearAuthToken();
    await _clearStoredAuth();
  }

  Future<void> _clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userTokenKey);
    await prefs.remove(AppConstants.userDataKey);
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  bool _isValidEmail(String email) {
    return RegExp(AppConstants.emailRegex).hasMatch(email);
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': 'flutter',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  String _getErrorMessage(dynamic error) {
    if (error is ApiException) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  void dispose() {
    _apiService.dispose();
  }
}

class AuthResult {
  final bool isSuccess;
  final UserModel? user;
  final String? errorMessage;

  AuthResult._(this.isSuccess, this.user, this.errorMessage);

  factory AuthResult.success(UserModel? user) {
    return AuthResult._(true, user, null);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, null, message);
  }

  bool get isFailure => !isSuccess;
}
