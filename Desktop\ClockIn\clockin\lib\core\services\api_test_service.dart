import 'package:flutter/foundation.dart';
import 'api_service.dart';

/// Service for testing API connectivity and endpoints
class ApiTestService {
  static final ApiTestService _instance = ApiTestService._internal();
  factory ApiTestService() => _instance;
  ApiTestService._internal();

  final ApiService _apiService = ApiService();

  /// Test API health check
  Future<bool> testApiHealth() async {
    try {
      debugPrint('Testing API health...');
      final response = await _apiService.get('/health');
      
      if (response['success'] == true) {
        debugPrint('✅ API Health Check: SUCCESS');
        debugPrint('API Message: ${response['message']}');
        debugPrint('API Version: ${response['version']}');
        debugPrint('Timestamp: ${response['timestamp']}');
        return true;
      } else {
        debugPrint('❌ API Health Check: FAILED');
        debugPrint('Response: $response');
        return false;
      }
    } catch (e) {
      debugPrint('❌ API Health Check: ERROR - $e');
      return false;
    }
  }

  /// Test login endpoint with dummy credentials
  Future<bool> testLoginEndpoint() async {
    try {
      debugPrint('Testing login endpoint...');
      
      // Use dummy credentials for testing
      final loginData = {
        'email': '<EMAIL>',
        'password': 'password123',
        'device_info': {
          'platform': 'flutter',
          'timestamp': DateTime.now().toIso8601String(),
        },
      };

      final response = await _apiService.post('/login', loginData);
      
      // We expect this to fail with invalid credentials, but the endpoint should respond
      if (response.containsKey('success')) {
        debugPrint('✅ Login Endpoint: RESPONDING');
        debugPrint('Response: ${response['message']}');
        return true;
      } else {
        debugPrint('❌ Login Endpoint: UNEXPECTED RESPONSE');
        debugPrint('Response: $response');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Login Endpoint: ERROR - $e');
      return false;
    }
  }

  /// Test all major endpoints without authentication
  Future<Map<String, bool>> testPublicEndpoints() async {
    final results = <String, bool>{};

    // Test health endpoint (if exists)
    results['health'] = await _testEndpoint('/health', 'GET');

    // Test login endpoint
    results['login'] = await testLoginEndpoint();

    return results;
  }

  /// Test authenticated endpoints (requires valid token)
  Future<Map<String, bool>> testAuthenticatedEndpoints() async {
    final results = <String, bool>{};

    // Test me endpoint
    results['me'] = await _testEndpoint('/me', 'GET');

    // Test my sites
    results['my-sites'] = await _testEndpoint('/my-sites', 'GET');

    // Test check location (needs data)
    results['check-location'] = await _testEndpointWithData('/check-location', 'POST', {
      'site_id': 'test-site-id',
      'latitude': 40.7128,
      'longitude': -74.0060,
    });

    // Test logout
    results['logout'] = await _testEndpoint('/logout', 'POST');

    return results;
  }

  /// Helper method to test individual endpoints
  Future<bool> _testEndpoint(String endpoint, String method) async {
    try {
      debugPrint('Testing $method $endpoint...');

      Map<String, dynamic> response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _apiService.get(endpoint);
          break;
        case 'POST':
          response = await _apiService.post(endpoint, {});
          break;
        default:
          debugPrint('❌ Unsupported method: $method');
          return false;
      }

      if (response.containsKey('success')) {
        debugPrint('✅ $method $endpoint: RESPONDING');
        if (response['success'] == false) {
          debugPrint('Response: ${response['message']}');
        }
        return true;
      } else {
        debugPrint('❌ $method $endpoint: UNEXPECTED RESPONSE');
        return false;
      }
    } catch (e) {
      debugPrint('❌ $method $endpoint: ERROR - $e');
      return false;
    }
  }

  /// Helper method to test endpoints with data
  Future<bool> _testEndpointWithData(String endpoint, String method, Map<String, dynamic> data) async {
    try {
      debugPrint('Testing $method $endpoint with data...');

      Map<String, dynamic> response;

      switch (method.toUpperCase()) {
        case 'POST':
          response = await _apiService.post(endpoint, data);
          break;
        case 'PUT':
          response = await _apiService.put(endpoint, data);
          break;
        default:
          debugPrint('❌ Unsupported method: $method');
          return false;
      }

      if (response.containsKey('success')) {
        debugPrint('✅ $method $endpoint: RESPONDING');
        if (response['success'] == false) {
          debugPrint('Response: ${response['message']}');
        }
        return true;
      } else {
        debugPrint('❌ $method $endpoint: UNEXPECTED RESPONSE');
        return false;
      }
    } catch (e) {
      debugPrint('❌ $method $endpoint: ERROR - $e');
      return false;
    }
  }

  /// Run comprehensive API tests
  Future<void> runComprehensiveTests() async {
    debugPrint('\n🚀 Starting Comprehensive API Tests...\n');

    // Initialize API service
    _apiService.initialize();

    // Test public endpoints
    debugPrint('📋 Testing Public Endpoints:');
    final publicResults = await testPublicEndpoints();
    _printResults(publicResults);

    // Test authenticated endpoints (will likely fail without token)
    debugPrint('\n📋 Testing Authenticated Endpoints (without token):');
    final authResults = await testAuthenticatedEndpoints();
    _printResults(authResults);

    // Summary
    final totalTests = publicResults.length + authResults.length;
    final passedTests = publicResults.values.where((v) => v).length + 
                       authResults.values.where((v) => v).length;

    debugPrint('\n📊 Test Summary:');
    debugPrint('Total Tests: $totalTests');
    debugPrint('Passed: $passedTests');
    debugPrint('Failed: ${totalTests - passedTests}');
    debugPrint('Success Rate: ${((passedTests / totalTests) * 100).toStringAsFixed(1)}%');

    if (publicResults['health'] == true) {
      debugPrint('\n✅ API is reachable and responding!');
      debugPrint('You can now proceed with authentication and other features.');
    } else {
      debugPrint('\n❌ API is not reachable!');
      debugPrint('Please check:');
      debugPrint('1. Backend server is running on http://localhost:8081');
      debugPrint('2. Network connectivity');
      debugPrint('3. Firewall settings');
      debugPrint('4. API base URL in app_constants.dart');
    }
  }

  /// Print test results in a formatted way
  void _printResults(Map<String, bool> results) {
    results.forEach((endpoint, success) {
      final status = success ? '✅' : '❌';
      debugPrint('  $status $endpoint');
    });
  }

  /// Test specific endpoint with custom data
  Future<Map<String, dynamic>?> testCustomEndpoint(
    String endpoint,
    String method, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParams,
  }) async {
    try {
      debugPrint('Testing custom endpoint: $method $endpoint');
      
      Map<String, dynamic> response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _apiService.get(endpoint, queryParameters: queryParams);
          break;
        case 'POST':
          response = await _apiService.post(endpoint, data ?? {}, queryParameters: queryParams);
          break;
        case 'PUT':
          response = await _apiService.put(endpoint, data ?? {}, queryParameters: queryParams);
          break;
        case 'DELETE':
          response = await _apiService.delete(endpoint, queryParameters: queryParams);
          break;
        default:
          debugPrint('❌ Unsupported method: $method');
          return null;
      }

      debugPrint('Response: $response');
      return response;
    } catch (e) {
      debugPrint('❌ Custom endpoint test failed: $e');
      return null;
    }
  }
}
