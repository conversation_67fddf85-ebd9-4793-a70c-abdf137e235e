import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'),
    Locale('fr', 'FR'),
    Locale('ar', 'SA'),
  ];

  // App General
  String get appName => _localizedValues[locale.languageCode]!['app_name']!;
  String get welcome => _localizedValues[locale.languageCode]!['welcome']!;
  String get loading => _localizedValues[locale.languageCode]!['loading']!;
  String get error => _localizedValues[locale.languageCode]!['error']!;
  String get success => _localizedValues[locale.languageCode]!['success']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;
  String get confirm => _localizedValues[locale.languageCode]!['confirm']!;
  String get save => _localizedValues[locale.languageCode]!['save']!;
  String get delete => _localizedValues[locale.languageCode]!['delete']!;
  String get edit => _localizedValues[locale.languageCode]!['edit']!;
  String get retry => _localizedValues[locale.languageCode]!['retry']!;

  // Authentication
  String get login => _localizedValues[locale.languageCode]!['login']!;
  String get logout => _localizedValues[locale.languageCode]!['logout']!;
  String get email => _localizedValues[locale.languageCode]!['email']!;
  String get password => _localizedValues[locale.languageCode]!['password']!;
  String get forgotPassword => _localizedValues[locale.languageCode]!['forgot_password']!;
  String get logoutConfirmation => _localizedValues[locale.languageCode]!['logout_confirmation']!;

  // Time Tracking
  String get clockIn => _localizedValues[locale.languageCode]!['clock_in']!;
  String get clockOut => _localizedValues[locale.languageCode]!['clock_out']!;
  String get workingTime => _localizedValues[locale.languageCode]!['working_time']!;
  String get currentlyWorking => _localizedValues[locale.languageCode]!['currently_working']!;
  String get notAtWorksite => _localizedValues[locale.languageCode]!['not_at_worksite']!;

  // Location
  String get location => _localizedValues[locale.languageCode]!['location']!;
  String get distance => _localizedValues[locale.languageCode]!['distance']!;
  String get nearbyWorksites => _localizedValues[locale.languageCode]!['nearby_worksites']!;

  static const Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'app_name': 'ClockIn',
      'welcome': 'Welcome',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'retry': 'Retry',
      'login': 'Login',
      'logout': 'Logout',
      'email': 'Email',
      'password': 'Password',
      'forgot_password': 'Forgot Password?',
      'logout_confirmation': 'Are you sure you want to logout?',
      'clock_in': 'Clock In',
      'clock_out': 'Clock Out',
      'working_time': 'Working Time',
      'currently_working': 'Currently Working',
      'not_at_worksite': 'Not at Worksite',
      'location': 'Location',
      'distance': 'Distance',
      'nearby_worksites': 'Nearby Worksites',
    },
    'fr': {
      'app_name': 'ClockIn',
      'welcome': 'Bienvenue',
      'loading': 'Chargement...',
      'error': 'Erreur',
      'success': 'Succès',
      'cancel': 'Annuler',
      'confirm': 'Confirmer',
      'save': 'Enregistrer',
      'delete': 'Supprimer',
      'edit': 'Modifier',
      'retry': 'Réessayer',
      'login': 'Connexion',
      'logout': 'Déconnexion',
      'email': 'Email',
      'password': 'Mot de passe',
      'forgot_password': 'Mot de passe oublié?',
      'logout_confirmation': 'Êtes-vous sûr de vouloir vous déconnecter?',
      'clock_in': 'Pointer',
      'clock_out': 'Dépointer',
      'working_time': 'Temps de travail',
      'currently_working': 'Actuellement au travail',
      'not_at_worksite': 'Pas sur le chantier',
      'location': 'Localisation',
      'distance': 'Distance',
      'nearby_worksites': 'Chantiers à proximité',
    },
    'ar': {
      'app_name': 'ClockIn',
      'welcome': 'مرحباً',
      'loading': 'جاري التحميل...',
      'error': 'خطأ',
      'success': 'نجح',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'retry': 'إعادة المحاولة',
      'login': 'تسجيل الدخول',
      'logout': 'تسجيل الخروج',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'forgot_password': 'نسيت كلمة المرور؟',
      'logout_confirmation': 'هل أنت متأكد من تسجيل الخروج؟',
      'clock_in': 'تسجيل الحضور',
      'clock_out': 'تسجيل الانصراف',
      'working_time': 'وقت العمل',
      'currently_working': 'يعمل حالياً',
      'not_at_worksite': 'ليس في موقع العمل',
      'location': 'الموقع',
      'distance': 'المسافة',
      'nearby_worksites': 'مواقع العمل القريبة',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'fr', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
