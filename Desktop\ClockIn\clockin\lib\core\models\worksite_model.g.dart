// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worksite_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WorksiteModelAdapter extends TypeAdapter<WorksiteModel> {
  @override
  final int typeId = 2;

  @override
  WorksiteModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WorksiteModel(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      latitude: fields[3] as double,
      longitude: fields[4] as double,
      radius: fields[5] as double,
      address: fields[6] as String?,
      type: fields[7] as WorksiteType,
      isActive: fields[8] as bool,
      createdAt: fields[9] as DateTime,
      createdBy: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, WorksiteModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.latitude)
      ..writeByte(4)
      ..write(obj.longitude)
      ..writeByte(5)
      ..write(obj.radius)
      ..writeByte(6)
      ..write(obj.address)
      ..writeByte(7)
      ..write(obj.type)
      ..writeByte(8)
      ..write(obj.isActive)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.createdBy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorksiteModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WorksiteTypeAdapter extends TypeAdapter<WorksiteType> {
  @override
  final int typeId = 3;

  @override
  WorksiteType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return WorksiteType.construction;
      case 1:
        return WorksiteType.office;
      case 2:
        return WorksiteType.warehouse;
      case 3:
        return WorksiteType.factory;
      case 4:
        return WorksiteType.retail;
      case 5:
        return WorksiteType.other;
      default:
        return WorksiteType.construction;
    }
  }

  @override
  void write(BinaryWriter writer, WorksiteType obj) {
    switch (obj) {
      case WorksiteType.construction:
        writer.writeByte(0);
        break;
      case WorksiteType.office:
        writer.writeByte(1);
        break;
      case WorksiteType.warehouse:
        writer.writeByte(2);
        break;
      case WorksiteType.factory:
        writer.writeByte(3);
        break;
      case WorksiteType.retail:
        writer.writeByte(4);
        break;
      case WorksiteType.other:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorksiteTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
