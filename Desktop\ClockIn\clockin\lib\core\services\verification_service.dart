import 'package:flutter/foundation.dart';
import 'api_service.dart';

class VerificationService {
  static final VerificationService _instance = VerificationService._internal();
  factory VerificationService() => _instance;
  VerificationService._internal();

  final ApiService _apiService = ApiService();

  /// Verify user location
  Future<VerificationResult> verifyLocation({
    required double latitude,
    required double longitude,
    String? notes,
  }) async {
    try {
      final data = {
        'latitude': latitude,
        'longitude': longitude,
        if (notes != null) 'notes': notes,
      };

      final response = await _apiService.post('/verify-location', data);

      if (response['success'] == true) {
        return VerificationResult(
          success: true,
          message: _extractMessage(response['message']) ?? 'Location verified successfully',
          data: response['data'],
        );
      }

      return VerificationResult(
        success: false,
        message: _extractMessage(response['message']) ?? 'Location verification failed',
        data: null,
      );
    } catch (e) {
      debugPrint('Failed to verify location: $e');
      return VerificationResult(
        success: false,
        message: 'Error verifying location: $e',
        data: null,
      );
    }
  }

  /// Request location verification (admin only)
  Future<bool> requestVerification({
    required String userId,
    String? message,
  }) async {
    try {
      final data = {
        'user_id': userId,
        if (message != null) 'message': message,
      };

      final response = await _apiService.post('/request-verification', data);
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to request verification: $e');
      return false;
    }
  }

  /// Get verifications (admin only)
  Future<List<VerificationData>> getVerifications({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (userId != null) queryParams['user_id'] = userId;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _apiService.get('/verifications', queryParameters: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => VerificationData.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get verifications: $e');
      return [];
    }
  }

  /// Extract message from multilingual response
  String? _extractMessage(dynamic message) {
    if (message is String) return message;
    if (message is Map<String, dynamic>) {
      // Try to get message in preferred language order: fr, en, ar
      return message['fr'] ?? message['en'] ?? message['ar'];
    }
    return null;
  }
}

class VerificationResult {
  final bool success;
  final String message;
  final dynamic data;

  VerificationResult({
    required this.success,
    required this.message,
    this.data,
  });
}

class VerificationData {
  final String id;
  final String userId;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final String? notes;
  final String? status;

  VerificationData({
    required this.id,
    required this.userId,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    this.notes,
    this.status,
  });

  factory VerificationData.fromJson(Map<String, dynamic> json) {
    return VerificationData(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      notes: json['notes'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
      'status': status,
    };
  }
}
