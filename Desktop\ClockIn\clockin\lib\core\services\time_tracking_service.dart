import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/time_entry_model.dart';
import '../models/worksite_model.dart';
import '../models/user_model.dart';
import 'location_service.dart';
import 'security_service.dart';
import 'api_service.dart';
import 'notification_service.dart';

// Classe pour les statistiques de temps de travail
class WorkingTimeStats {
  final Duration totalWorked;
  final Duration totalBreaks;
  final int clockInCount;
  final double efficiency;

  WorkingTimeStats({
    required this.totalWorked,
    required this.totalBreaks,
    required this.clockInCount,
    required this.efficiency,
  });

  String get formattedTotalWorked {
    final hours = totalWorked.inHours;
    final minutes = totalWorked.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  String get formattedTotalBreaks {
    final hours = totalBreaks.inHours;
    final minutes = totalBreaks.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  String get formattedEfficiency {
    return '${efficiency.toStringAsFixed(1)}%';
  }
}

class TimeTrackingService {
  static final TimeTrackingService _instance = TimeTrackingService._internal();
  factory TimeTrackingService() => _instance;
  TimeTrackingService._internal();

  final LocationService _locationService = LocationService();
  final SecurityService _securityService = SecurityService();
  final ApiService _apiService = ApiService();
  final NotificationService _notificationService = NotificationService();

  TimeEntryModel? _currentEntry;
  Timer? _workingTimer;
  StreamController<Duration>? _workingDurationController;
  bool _isClockingIn = false;
  bool _isClockingOut = false;

  TimeEntryModel? get currentEntry => _currentEntry;
  bool get isWorking => _currentEntry != null;
  bool get isClockingIn => _isClockingIn;
  bool get isClockingOut => _isClockingOut;
  Stream<Duration>? get workingDurationStream => _workingDurationController?.stream;

  Future<void> initialize() async {
    try {
      // Initialize location service
      await _locationService.requestLocationPermission();
      
      // Initialize notification service
      await _notificationService.initialize();
      
      // Load current time entry if exists
      await _loadCurrentTimeEntry();
      
      // Start working timer if currently working
      if (_currentEntry != null) {
        _startWorkingTimer();
      }
    } catch (e) {
      debugPrint('TimeTrackingService initialization failed: $e');
    }
  }

  Future<void> _loadCurrentTimeEntry() async {
    try {
      final response = await _apiService.get('/time-entries/current');
      if (response['success'] == true && response['data'] != null) {
        _currentEntry = TimeEntryModel.fromJson(response['data']);
      }
    } catch (e) {
      debugPrint('Failed to load current time entry: $e');
    }
  }

  Future<ClockInResult> clockIn(WorksiteModel worksite, UserModel user) async {
    if (_isClockingIn) {
      return ClockInResult.failure('Clock in already in progress');
    }

    _isClockingIn = true;

    try {
      // Perform comprehensive location and security check
      final locationCheck = await _locationService.checkLocationForClockIn(worksite);
      
      if (!locationCheck.canClockIn) {
        return ClockInResult.failure(locationCheck.reason);
      }

      // Perform additional security checks
      final securityCheck = await _securityService.performSecurityCheck();
      if (!securityCheck.isSecure) {
        await _logSecurityViolation(
          'Clock in attempt with security issues: ${securityCheck.issues.join(', ')}',
          user.id,
          worksite.id,
        );
        return ClockInResult.failure(
          'Security check failed: ${securityCheck.issues.first}'
        );
      }

      // Generate device fingerprint for tracking
      final deviceFingerprint = await _securityService.generateDeviceFingerprint();

      // Create time entry
      final clockInData = {
        'user_id': user.id,
        'worksite_id': worksite.id,
        'clock_in_time': DateTime.now().toIso8601String(),
        'clock_in_location': locationCheck.location!.toJson(),
        'device_fingerprint': deviceFingerprint,
        'security_info': securityCheck.deviceInfo,
      };

      final response = await _apiService.post('/time-entries/clock-in', clockInData);

      if (response['success'] == true) {
        _currentEntry = TimeEntryModel.fromJson(response['data']);
        
        // Start location tracking
        await _locationService.startLocationTracking();
        
        // Start working timer
        _startWorkingTimer();
        
        // Log successful clock in
        await _logClockInEvent(user.id, worksite.id, 'success');
        
        return ClockInResult.success(_currentEntry!);
      } else {
        await _logClockInEvent(user.id, worksite.id, 'failed', response['message']);
        return ClockInResult.failure(response['message'] ?? 'Clock in failed');
      }
    } catch (e) {
      await _logClockInEvent(user.id, worksite.id, 'error', e.toString());
      return ClockInResult.failure('Clock in error: ${e.toString()}');
    } finally {
      _isClockingIn = false;
    }
  }

  Future<ClockOutResult> clockOut(UserModel user) async {
    if (_currentEntry == null) {
      return ClockOutResult.failure('No active time entry found');
    }

    if (_isClockingOut) {
      return ClockOutResult.failure('Clock out already in progress');
    }

    _isClockingOut = true;

    try {
      // Get current location for clock out
      final currentLocation = await _locationService.getCurrentLocation();
      if (currentLocation == null) {
        return ClockOutResult.failure('Unable to get current location');
      }

      // Perform security check
      final securityCheck = await _securityService.performSecurityCheck();
      if (!securityCheck.isSecure) {
        await _logSecurityViolation(
          'Clock out attempt with security issues: ${securityCheck.issues.join(', ')}',
          user.id,
          _currentEntry!.worksiteId,
        );
        // Allow clock out even with security issues, but log them
      }

      // Calculate total duration
      final clockOutTime = DateTime.now();
      final totalDuration = clockOutTime.difference(_currentEntry!.clockInTime);

      final clockOutData = {
        'entry_id': _currentEntry!.id,
        'clock_out_time': clockOutTime.toIso8601String(),
        'clock_out_location': currentLocation.toJson(),
        'total_duration': totalDuration.inSeconds,
      };

      final response = await _apiService.post('/time-entries/clock-out', clockOutData);

      if (response['success'] == true) {
        final completedEntry = _currentEntry!.copyWith(
          clockOutTime: clockOutTime,
          clockOutLocation: currentLocation,
          totalDuration: totalDuration,
          status: TimeEntryStatus.completed,
        );

        // Stop tracking
        await _stopTracking();
        
        // Log successful clock out
        await _logClockOutEvent(user.id, _currentEntry!.worksiteId, 'success');
        
        _currentEntry = null;
        
        return ClockOutResult.success(completedEntry);
      } else {
        await _logClockOutEvent(user.id, _currentEntry!.worksiteId, 'failed', response['message']);
        return ClockOutResult.failure(response['message'] ?? 'Clock out failed');
      }
    } catch (e) {
      await _logClockOutEvent(user.id, _currentEntry!.worksiteId, 'error', e.toString());
      return ClockOutResult.failure('Clock out error: ${e.toString()}');
    } finally {
      _isClockingOut = false;
    }
  }

  void _startWorkingTimer() {
    _workingDurationController = StreamController<Duration>.broadcast();
    
    _workingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_currentEntry != null) {
        final duration = DateTime.now().difference(_currentEntry!.clockInTime);
        _workingDurationController?.add(duration);
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _stopTracking() async {
    _workingTimer?.cancel();
    _workingTimer = null;
    await _workingDurationController?.close();
    _workingDurationController = null;
    await _locationService.stopLocationTracking();
  }

  Future<void> handleLocationVerificationRequest(Map<String, dynamic> data) async {
    try {
      // Get current location
      final currentLocation = await _locationService.getCurrentLocation();
      if (currentLocation == null) {
        throw Exception('Unable to get current location');
      }

      // Send verification response
      await _apiService.post('/location/verify', {
        'verification_id': data['verification_id'],
        'location': currentLocation.toJson(),
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to handle location verification: $e');
    }
  }

  Future<List<TimeEntryModel>> getTimeEntryHistory({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();
      if (limit != null) queryParams['limit'] = limit.toString();

      final response = await _apiService.get('/time-entries/history', queryParameters: queryParams);
      
      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => TimeEntryModel.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Failed to get time entry history: $e');
      return [];
    }
  }

  Future<WorkingTimeStats> getTodayStats() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final entries = await getTimeEntryHistory(
        startDate: startOfDay,
        endDate: endOfDay,
      );

      Duration totalWorked = Duration.zero;
      Duration totalBreaks = Duration.zero;
      int clockInCount = entries.length;

      for (final entry in entries) {
        if (entry.totalDuration != null) {
          totalWorked += entry.totalDuration!;
        }
      }

      // Add current session if working
      if (_currentEntry != null) {
        final currentDuration = DateTime.now().difference(_currentEntry!.clockInTime);
        totalWorked += currentDuration;
      }

      return WorkingTimeStats(
        totalWorked: totalWorked,
        totalBreaks: totalBreaks,
        clockInCount: clockInCount,
        efficiency: _calculateEfficiency(totalWorked, totalBreaks),
      );
    } catch (e) {
      debugPrint('Failed to get today stats: $e');
      return WorkingTimeStats(
        totalWorked: Duration.zero,
        totalBreaks: Duration.zero,
        clockInCount: 0,
        efficiency: 0.0,
      );
    }
  }

  double _calculateEfficiency(Duration worked, Duration breaks) {
    final totalTime = worked + breaks;
    if (totalTime.inMinutes == 0) return 0.0;
    return (worked.inMinutes / totalTime.inMinutes) * 100;
  }

  Future<void> _logClockInEvent(String userId, String worksiteId, String status, [String? details]) async {
    try {
      await _apiService.post('/security/log-clock-event', {
        'user_id': userId,
        'worksite_id': worksiteId,
        'event_type': 'clock_in',
        'status': status,
        'details': details,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to log clock in event: $e');
    }
  }

  Future<void> _logClockOutEvent(String userId, String worksiteId, String status, [String? details]) async {
    try {
      await _apiService.post('/security/log-clock-event', {
        'user_id': userId,
        'worksite_id': worksiteId,
        'event_type': 'clock_out',
        'status': status,
        'details': details,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to log clock out event: $e');
    }
  }

  Future<void> _logSecurityViolation(String violation, String userId, String worksiteId) async {
    try {
      await _apiService.post('/security/log-violation', {
        'user_id': userId,
        'worksite_id': worksiteId,
        'violation': violation,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to log security violation: $e');
    }
  }

  void dispose() {
    _stopTracking();
  }
}

class ClockInResult {
  final bool isSuccess;
  final TimeEntryModel? entry;
  final String? errorMessage;

  ClockInResult._(this.isSuccess, this.entry, this.errorMessage);

  factory ClockInResult.success(TimeEntryModel entry) {
    return ClockInResult._(true, entry, null);
  }

  factory ClockInResult.failure(String message) {
    return ClockInResult._(false, null, message);
  }
}

class ClockOutResult {
  final bool isSuccess;
  final TimeEntryModel? entry;
  final String? errorMessage;

  ClockOutResult._(this.isSuccess, this.entry, this.errorMessage);

  factory ClockOutResult.success(TimeEntryModel entry) {
    return ClockOutResult._(true, entry, null);
  }

  factory ClockOutResult.failure(String message) {
    return ClockOutResult._(false, null, message);
  }
}


