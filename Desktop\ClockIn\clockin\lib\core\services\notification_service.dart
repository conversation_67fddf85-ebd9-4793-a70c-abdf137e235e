import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  FirebaseMessaging? _messaging;
  String? _fcmToken;

  String? get fcmToken => _fcmToken;

  Future<void> initialize() async {
    try {
      _messaging = FirebaseMessaging.instance;

      // Request permission
      await _requestPermission();

      // Get FCM token
      await _getFCMToken();

      // Configure message handlers
      _configureMessageHandlers();

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    } catch (e) {
      debugPrint('Notification service initialization failed: $e');
    }
  }

  Future<void> _requestPermission() async {
    final settings = await _messaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('Notification permission status: ${settings.authorizationStatus}');
  }

  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _messaging!.getToken();
      debugPrint('FCM Token: $_fcmToken');
      
      // Save token locally
      if (_fcmToken != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', _fcmToken!);
      }
    } catch (e) {
      debugPrint('Failed to get FCM token: $e');
    }
  }

  void _configureMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Received foreground message: ${message.messageId}');
      _handleMessage(message);
    });

    // Handle message when app is opened from notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('App opened from notification: ${message.messageId}');
      _handleMessageTap(message);
    });

    // Handle initial message when app is launched from notification
    _messaging!.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        debugPrint('App launched from notification: ${message.messageId}');
        _handleMessageTap(message);
      }
    });

    // Listen for token refresh
    _messaging!.onTokenRefresh.listen((String token) {
      _fcmToken = token;
      debugPrint('FCM Token refreshed: $token');
      _saveFCMToken(token);
    });
  }

  Future<void> _saveFCMToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
      
      // TODO: Send token to server
      // await ApiService().post('/api/update-fcm-token', {'token': token});
    } catch (e) {
      debugPrint('Failed to save FCM token: $e');
    }
  }

  void _handleMessage(RemoteMessage message) {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      _showLocalNotification(
        title: notification.title ?? 'ClockIn',
        body: notification.body ?? '',
        data: data,
      );
    }
  }

  void _handleMessageTap(RemoteMessage message) {
    final data = message.data;
    final type = data['type'];

    switch (type) {
      case 'location_verification':
        _handleLocationVerificationRequest(data);
        break;
      case 'site_assignment':
        _handleSiteAssignment(data);
        break;
      case 'general':
      default:
        // Handle general notification
        break;
    }
  }

  void _handleLocationVerificationRequest(Map<String, dynamic> data) {
    // Navigate to location verification screen
    // This would typically be handled by a navigation service
    debugPrint('Location verification requested: $data');
  }

  void _handleSiteAssignment(Map<String, dynamic> data) {
    // Handle site assignment notification
    debugPrint('Site assignment notification: $data');
  }

  void _showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) {
    // This would typically use a local notification plugin
    // For now, we'll just log it
    debugPrint('Local notification: $title - $body');
  }

  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging!.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Failed to subscribe to topic $topic: $e');
    }
  }

  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging!.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Failed to unsubscribe from topic $topic: $e');
    }
  }

  Future<void> sendLocationVerificationRequest(String userId) async {
    // This would typically be called from the admin interface
    // to request location verification from an employee
    try {
      // TODO: Implement server-side notification sending
      debugPrint('Sending location verification request to user: $userId');
    } catch (e) {
      debugPrint('Failed to send location verification request: $e');
    }
  }

  Future<void> sendSiteAssignmentNotification(
    List<String> userIds,
    String siteName,
  ) async {
    try {
      // TODO: Implement server-side notification sending
      debugPrint('Sending site assignment notification to users: $userIds');
    } catch (e) {
      debugPrint('Failed to send site assignment notification: $e');
    }
  }

  void dispose() {
    // Clean up resources if needed
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('Handling background message: ${message.messageId}');
  
  // Handle background message
  final data = message.data;
  final type = data['type'];

  switch (type) {
    case 'location_verification':
      // Store verification request for when app is opened
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_verification', jsonEncode(data));
      break;
    default:
      break;
  }
}
