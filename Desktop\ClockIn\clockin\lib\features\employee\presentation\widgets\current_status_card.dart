import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localizations.dart';
import '../providers/time_tracking_provider.dart';

class CurrentStatusCard extends ConsumerWidget {
  const CurrentStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizations = AppLocalizations.of(context)!;
    final timeTrackingState = ref.watch(timeTrackingProvider);
    final isWorking = timeTrackingState.isWorking;
    final currentEntry = timeTrackingState.currentEntry;
    final currentDuration = ref.watch(currentWorkingDurationProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: isWorking
              ? LinearGradient(
                  colors: [
                    AppTheme.accentColor.withOpacity(0.1),
                    AppTheme.accentColor.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [
                    Colors.grey.shade100,
                    Colors.grey.shade50,
                  ],
                ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isWorking
                        ? AppTheme.accentColor
                        : Colors.grey.shade400,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isWorking
                        ? FontAwesomeIcons.userClock
                        : FontAwesomeIcons.userXmark,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Status',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isWorking
                            ? localizations.currentlyWorking
                            : 'Not Working',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status Indicator with Animation
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: isWorking
                        ? AppTheme.accentColor
                        : Colors.grey.shade400,
                    shape: BoxShape.circle,
                    boxShadow: isWorking
                        ? [
                            BoxShadow(
                              color: AppTheme.accentColor.withOpacity(0.3),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ]
                        : null,
                  ),
                ),
              ],
            ),

            if (isWorking && currentEntry != null) ...[
              const SizedBox(height: 20),
              const Divider(),
              const SizedBox(height: 16),

              // Working Details
              _buildWorkingDetails(currentEntry, currentDuration, localizations),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWorkingDetails(currentEntry, Duration? currentDuration, AppLocalizations localizations) {
    final startTime = currentEntry.clockInTime;

    return Column(
      children: [
        // Start Time
        Row(
          children: [
            Icon(
              FontAwesomeIcons.clock,
              size: 16,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 12),
            Text(
              'Started at',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
            ),
            const Spacer(),
            Text(
              _formatTime(startTime),
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Current Duration
        Row(
          children: [
            Icon(
              FontAwesomeIcons.stopwatch,
              size: 16,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(width: 12),
            Text(
              localizations.workingTime,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
            ),
            const Spacer(),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                currentDuration != null
                    ? _formatDuration(currentDuration)
                    : _formatDuration(DateTime.now().difference(startTime)),
                key: ValueKey(currentDuration?.inSeconds ?? 0),
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Location
        if (currentEntry.clockInLocation.address != null)
          Row(
            children: [
              Icon(
                FontAwesomeIcons.locationDot,
                size: 16,
                color: AppTheme.textSecondary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  currentEntry.clockInLocation.address!,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Progress Bar (visual representation of work day)
        _buildWorkDayProgress(currentDuration ?? DateTime.now().difference(startTime)),
      ],
    );
  }

  Widget _buildWorkDayProgress(Duration currentDuration) {
    const standardWorkDay = Duration(hours: 8);
    final progress = (currentDuration.inMinutes / standardWorkDay.inMinutes).clamp(0.0, 1.0);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Work Day Progress',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.accentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.accentColor,
                    AppTheme.secondaryColor,
                  ],
                ),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour == 0 ? 12 : (time.hour > 12 ? time.hour - 12 : time.hour);
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    return '${hours.toString().padLeft(2, '0')}:'
           '${minutes.toString().padLeft(2, '0')}:'
           '${seconds.toString().padLeft(2, '0')}';
  }
}
