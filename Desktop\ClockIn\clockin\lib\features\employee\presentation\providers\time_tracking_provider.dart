import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/time_entry_model.dart';
import '../../../../core/models/worksite_model.dart';
import '../../../../core/services/location_service.dart';
import '../../../../core/services/time_tracking_service.dart';
import '../../../../core/services/api_service.dart';
import '../../../auth/presentation/providers/auth_provider.dart';

// Time Tracking State
class TimeTrackingState {
  final TimeEntryModel? currentEntry;
  final List<WorksiteModel> nearbyWorksites;
  final WorksiteModel? selectedWorksite;
  final bool isLoading;
  final String? errorMessage;
  final bool canClockIn;
  final LocationData? currentLocation;
  final WorkingTimeStats? todayStats;
  final Duration? currentWorkingDuration;

  const TimeTrackingState({
    this.currentEntry,
    this.nearbyWorksites = const [],
    this.selectedWorksite,
    this.isLoading = false,
    this.errorMessage,
    this.canClockIn = false,
    this.currentLocation,
    this.todayStats,
    this.currentWorkingDuration,
  });

  TimeTrackingState copyWith({
    TimeEntryModel? currentEntry,
    List<WorksiteModel>? nearbyWorksites,
    WorksiteModel? selectedWorksite,
    bool? isLoading,
    String? errorMessage,
    bool? canClockIn,
    LocationData? currentLocation,
    WorkingTimeStats? todayStats,
    Duration? currentWorkingDuration,
  }) {
    return TimeTrackingState(
      currentEntry: currentEntry ?? this.currentEntry,
      nearbyWorksites: nearbyWorksites ?? this.nearbyWorksites,
      selectedWorksite: selectedWorksite ?? this.selectedWorksite,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      canClockIn: canClockIn ?? this.canClockIn,
      currentLocation: currentLocation ?? this.currentLocation,
      todayStats: todayStats ?? this.todayStats,
      currentWorkingDuration: currentWorkingDuration ?? this.currentWorkingDuration,
    );
  }

  bool get isWorking => currentEntry != null;
}

// Time Tracking Notifier
class TimeTrackingNotifier extends StateNotifier<TimeTrackingState> {
  final TimeTrackingService _timeTrackingService;
  final LocationService _locationService;
  final ApiService _apiService;
  final Ref _ref;

  TimeTrackingNotifier(
    this._timeTrackingService,
    this._locationService,
    this._apiService,
    this._ref,
  ) : super(const TimeTrackingState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    state = state.copyWith(isLoading: true);

    try {
      // Initialize time tracking service
      await _timeTrackingService.initialize();

      // Load current state
      await _loadCurrentState();

      // Load nearby worksites
      await _loadNearbyWorksites();

      // Load today's stats
      await _loadTodayStats();

      // Listen to working duration updates
      _listenToWorkingDuration();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Initialization failed: ${e.toString()}',
      );
    }
  }

  Future<void> _loadCurrentState() async {
    try {
      final currentEntry = _timeTrackingService.currentEntry;
      final currentLocation = await _locationService.getCurrentLocation();

      state = state.copyWith(
        currentEntry: currentEntry,
        currentLocation: currentLocation,
      );

      // Check if can clock in
      await _checkCanClockIn();
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadNearbyWorksites() async {
    try {
      final response = await _apiService.get('/api/worksites/nearby');
      if (response['success'] == true) {
        final worksites = (response['data'] as List)
            .map((json) => WorksiteModel.fromJson(json))
            .toList();

        final nearbyWorksites = await _locationService.getNearbyWorksites(worksites);
        state = state.copyWith(nearbyWorksites: nearbyWorksites);

        // Update clock in availability
        await _checkCanClockIn();
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadTodayStats() async {
    try {
      final stats = await _timeTrackingService.getTodayStats();
      state = state.copyWith(todayStats: stats);
    } catch (e) {
      // Handle error silently
    }
  }

  void _listenToWorkingDuration() {
    _timeTrackingService.workingDurationStream?.listen((duration) {
      state = state.copyWith(currentWorkingDuration: duration);
    });
  }

  Future<void> _checkCanClockIn() async {
    if (state.nearbyWorksites.isEmpty) {
      state = state.copyWith(canClockIn: false);
      return;
    }

    try {
      for (final worksite in state.nearbyWorksites) {
        final locationCheck = await _locationService.checkLocationForClockIn(worksite);
        if (locationCheck.canClockIn) {
          state = state.copyWith(
            canClockIn: true,
            selectedWorksite: worksite,
          );
          return;
        }
      }
      state = state.copyWith(canClockIn: false);
    } catch (e) {
      state = state.copyWith(canClockIn: false);
    }
  }

  Future<void> clockIn() async {
    if (!state.canClockIn || state.selectedWorksite == null) {
      throw Exception('Cannot clock in: not at worksite');
    }

    state = state.copyWith(isLoading: true);

    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) throw Exception('User not authenticated');

      final result = await _timeTrackingService.clockIn(state.selectedWorksite!, user);

      if (result.isSuccess) {
        state = state.copyWith(
          currentEntry: result.entry,
          isLoading: false,
          errorMessage: null,
        );

        // Refresh stats
        await _loadTodayStats();
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: result.errorMessage,
        );
        throw Exception(result.errorMessage);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> clockOut() async {
    if (state.currentEntry == null) {
      throw Exception('No active time entry');
    }

    state = state.copyWith(isLoading: true);

    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) throw Exception('User not authenticated');

      final result = await _timeTrackingService.clockOut(user);

      if (result.isSuccess) {
        state = state.copyWith(
          currentEntry: null,
          isLoading: false,
          errorMessage: null,
          currentWorkingDuration: null,
        );

        // Refresh stats
        await _loadTodayStats();
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: result.errorMessage,
        );
        throw Exception(result.errorMessage);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> refreshData() async {
    await _loadCurrentState();
    await _loadNearbyWorksites();
    await _loadTodayStats();
  }

  Future<void> handleLocationVerificationRequest(Map<String, dynamic> data) async {
    try {
      await _timeTrackingService.handleLocationVerificationRequest(data);
    } catch (e) {
      state = state.copyWith(errorMessage: 'Location verification failed: ${e.toString()}');
    }
  }

  Future<List<TimeEntryModel>> getTimeEntryHistory({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      return await _timeTrackingService.getTimeEntryHistory(
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to load history: ${e.toString()}');
      return [];
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  @override
  void dispose() {
    _timeTrackingService.dispose();
    super.dispose();
  }
}

// Providers
final timeTrackingServiceProvider = Provider<TimeTrackingService>((ref) {
  return TimeTrackingService();
});

final locationServiceProvider = Provider<LocationService>((ref) {
  return LocationService();
});

final timeTrackingProvider = StateNotifierProvider<TimeTrackingNotifier, TimeTrackingState>((ref) {
  final timeTrackingService = ref.watch(timeTrackingServiceProvider);
  final locationService = ref.watch(locationServiceProvider);
  final apiService = ApiService();
  return TimeTrackingNotifier(timeTrackingService, locationService, apiService, ref);
});

// Computed providers
final currentTimeEntryProvider = Provider<TimeEntryModel?>((ref) {
  return ref.watch(timeTrackingProvider).currentEntry;
});

final canClockInProvider = Provider<bool>((ref) {
  return ref.watch(timeTrackingProvider).canClockIn;
});

final nearbyWorksitesProvider = Provider<List<WorksiteModel>>((ref) {
  return ref.watch(timeTrackingProvider).nearbyWorksites;
});

final isWorkingProvider = Provider<bool>((ref) {
  return ref.watch(timeTrackingProvider).isWorking;
});

final todayStatsProvider = Provider<WorkingTimeStats?>((ref) {
  return ref.watch(timeTrackingProvider).todayStats;
});

final currentWorkingDurationProvider = Provider<Duration?>((ref) {
  return ref.watch(timeTrackingProvider).currentWorkingDuration;
});
