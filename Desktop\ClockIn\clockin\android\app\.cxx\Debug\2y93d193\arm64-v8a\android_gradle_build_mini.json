{"buildFiles": ["C:\\flutter_windows_3.27.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ClockIn\\clockin\\android\\app\\.cxx\\Debug\\2y93d193\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ClockIn\\clockin\\android\\app\\.cxx\\Debug\\2y93d193\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}