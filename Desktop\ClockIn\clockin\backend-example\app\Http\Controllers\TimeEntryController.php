<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\TimeEntry;
use App\Models\Worksite;
use App\Services\LocationService;
use App\Services\SecurityService;
use Carbon\Carbon;

class TimeEntryController extends Controller
{
    protected $locationService;
    protected $securityService;

    public function __construct(LocationService $locationService, SecurityService $securityService)
    {
        $this->locationService = $locationService;
        $this->securityService = $securityService;
    }

    /**
     * Get current active time entry for authenticated user.
     */
    public function getCurrentEntry()
    {
        $user = Auth::user();
        
        $currentEntry = TimeEntry::where('user_id', $user->id)
            ->where('status', 'active')
            ->with('worksite')
            ->first();

        if (!$currentEntry) {
            return response()->json([
                'success' => true,
                'data' => null,
                'message' => 'No active time entry found'
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $this->formatTimeEntry($currentEntry)
        ]);
    }

    /**
     * Clock in - start a new time entry.
     */
    public function clockIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'worksite_id' => 'required|uuid|exists:worksites,id',
            'clock_in_location' => 'required|array',
            'clock_in_location.latitude' => 'required|numeric|between:-90,90',
            'clock_in_location.longitude' => 'required|numeric|between:-180,180',
            'clock_in_location.accuracy' => 'sometimes|numeric',
            'device_fingerprint' => 'sometimes|string',
            'security_info' => 'sometimes|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user already has an active time entry
        $existingEntry = TimeEntry::where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        if ($existingEntry) {
            return response()->json([
                'success' => false,
                'message' => 'You already have an active time entry. Please clock out first.'
            ], 400);
        }

        $worksite = Worksite::findOrFail($request->worksite_id);
        $location = $request->clock_in_location;

        // Verify location is within worksite radius
        $isWithinRadius = $this->locationService->isWithinRadius(
            $location['latitude'],
            $location['longitude'],
            $worksite->latitude,
            $worksite->longitude,
            $worksite->radius
        );

        if (!$isWithinRadius) {
            $distance = $this->locationService->calculateDistance(
                $location['latitude'],
                $location['longitude'],
                $worksite->latitude,
                $worksite->longitude
            );

            // Log security violation
            $this->securityService->logViolation([
                'user_id' => $user->id,
                'worksite_id' => $worksite->id,
                'violation_type' => 'location_outside_radius',
                'details' => "Attempted clock in from {$distance}m away (allowed: {$worksite->radius}m)",
                'latitude' => $location['latitude'],
                'longitude' => $location['longitude']
            ]);

            return response()->json([
                'success' => false,
                'message' => "You must be within {$worksite->radius}m of the worksite to clock in. You are {$distance}m away."
            ], 400);
        }

        // Perform security checks
        $securityCheck = $this->securityService->performSecurityCheck($request);
        if (!$securityCheck['is_secure']) {
            return response()->json([
                'success' => false,
                'message' => 'Security check failed: ' . implode(', ', $securityCheck['issues'])
            ], 400);
        }

        // Create time entry
        $timeEntry = TimeEntry::create([
            'id' => \Str::uuid(),
            'user_id' => $user->id,
            'worksite_id' => $worksite->id,
            'clock_in_time' => now(),
            'clock_in_latitude' => $location['latitude'],
            'clock_in_longitude' => $location['longitude'],
            'clock_in_accuracy' => $location['accuracy'] ?? null,
            'clock_in_address' => $this->locationService->getAddress($location['latitude'], $location['longitude']),
            'device_fingerprint' => $request->device_fingerprint,
            'status' => 'active'
        ]);

        // Log successful clock in
        $this->securityService->logClockEvent([
            'user_id' => $user->id,
            'worksite_id' => $worksite->id,
            'event_type' => 'clock_in',
            'status' => 'success',
            'time_entry_id' => $timeEntry->id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Clocked in successfully',
            'data' => $this->formatTimeEntry($timeEntry->load('worksite'))
        ]);
    }

    /**
     * Clock out - end current time entry.
     */
    public function clockOut(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'clock_out_location' => 'required|array',
            'clock_out_location.latitude' => 'required|numeric|between:-90,90',
            'clock_out_location.longitude' => 'required|numeric|between:-180,180',
            'clock_out_location.accuracy' => 'sometimes|numeric',
            'notes' => 'sometimes|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $location = $request->clock_out_location;

        // Find active time entry
        $timeEntry = TimeEntry::where('user_id', $user->id)
            ->where('status', 'active')
            ->with('worksite')
            ->first();

        if (!$timeEntry) {
            return response()->json([
                'success' => false,
                'message' => 'No active time entry found'
            ], 400);
        }

        $clockOutTime = now();
        $totalDuration = $clockOutTime->diffInSeconds($timeEntry->clock_in_time);

        // Update time entry
        $timeEntry->update([
            'clock_out_time' => $clockOutTime,
            'clock_out_latitude' => $location['latitude'],
            'clock_out_longitude' => $location['longitude'],
            'clock_out_accuracy' => $location['accuracy'] ?? null,
            'clock_out_address' => $this->locationService->getAddress($location['latitude'], $location['longitude']),
            'total_duration' => $totalDuration,
            'notes' => $request->notes,
            'status' => 'completed'
        ]);

        // Log successful clock out
        $this->securityService->logClockEvent([
            'user_id' => $user->id,
            'worksite_id' => $timeEntry->worksite_id,
            'event_type' => 'clock_out',
            'status' => 'success',
            'time_entry_id' => $timeEntry->id,
            'duration' => $totalDuration
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Clocked out successfully',
            'data' => $this->formatTimeEntry($timeEntry)
        ]);
    }

    /**
     * Get time entry history with filters.
     */
    public function getHistory(Request $request)
    {
        $user = Auth::user();
        
        $query = TimeEntry::where('user_id', $user->id)
            ->with('worksite')
            ->orderBy('clock_in_time', 'desc');

        // Apply filters
        if ($request->has('start_date')) {
            $query->where('clock_in_time', '>=', Carbon::parse($request->start_date));
        }

        if ($request->has('end_date')) {
            $query->where('clock_in_time', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        if ($request->has('worksite_id')) {
            $query->where('worksite_id', $request->worksite_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $limit = $request->input('limit', 50);
        $timeEntries = $query->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $timeEntries->map(function ($entry) {
                return $this->formatTimeEntry($entry);
            })
        ]);
    }

    /**
     * Get today's work statistics.
     */
    public function getTodayStats()
    {
        $user = Auth::user();
        $today = Carbon::today();

        $entries = TimeEntry::where('user_id', $user->id)
            ->whereDate('clock_in_time', $today)
            ->get();

        $totalWorked = $entries->sum('total_duration') ?? 0;
        $clockInCount = $entries->count();
        $activeEntry = $entries->where('status', 'active')->first();

        // Add current session time if working
        if ($activeEntry) {
            $currentDuration = now()->diffInSeconds($activeEntry->clock_in_time);
            $totalWorked += $currentDuration;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'total_worked_seconds' => $totalWorked,
                'total_worked_formatted' => $this->formatDuration($totalWorked),
                'clock_in_count' => $clockInCount,
                'is_currently_working' => $activeEntry !== null,
                'current_session_start' => $activeEntry?->clock_in_time,
                'efficiency_percentage' => $this->calculateEfficiency($totalWorked)
            ]
        ]);
    }

    /**
     * Format time entry for API response.
     */
    private function formatTimeEntry($timeEntry)
    {
        return [
            'id' => $timeEntry->id,
            'user_id' => $timeEntry->user_id,
            'worksite_id' => $timeEntry->worksite_id,
            'worksite' => $timeEntry->worksite ? [
                'id' => $timeEntry->worksite->id,
                'name' => $timeEntry->worksite->name,
                'address' => $timeEntry->worksite->address
            ] : null,
            'clock_in_time' => $timeEntry->clock_in_time,
            'clock_out_time' => $timeEntry->clock_out_time,
            'clock_in_location' => [
                'latitude' => $timeEntry->clock_in_latitude,
                'longitude' => $timeEntry->clock_in_longitude,
                'accuracy' => $timeEntry->clock_in_accuracy,
                'address' => $timeEntry->clock_in_address
            ],
            'clock_out_location' => $timeEntry->clock_out_latitude ? [
                'latitude' => $timeEntry->clock_out_latitude,
                'longitude' => $timeEntry->clock_out_longitude,
                'accuracy' => $timeEntry->clock_out_accuracy,
                'address' => $timeEntry->clock_out_address
            ] : null,
            'total_duration' => $timeEntry->total_duration,
            'total_duration_formatted' => $timeEntry->total_duration ? $this->formatDuration($timeEntry->total_duration) : null,
            'status' => $timeEntry->status,
            'notes' => $timeEntry->notes,
            'created_at' => $timeEntry->created_at,
            'updated_at' => $timeEntry->updated_at
        ];
    }

    /**
     * Format duration in seconds to HH:MM:SS.
     */
    private function formatDuration($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Calculate work efficiency percentage.
     */
    private function calculateEfficiency($workedSeconds)
    {
        $standardWorkDay = 8 * 3600; // 8 hours in seconds
        return min(100, round(($workedSeconds / $standardWorkDay) * 100, 1));
    }
}
