import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localizations.dart';
import '../providers/time_tracking_provider.dart';

class ClockInButton extends ConsumerStatefulWidget {
  const ClockInButton({super.key});

  @override
  ConsumerState<ClockInButton> createState() => _ClockInButtonState();
}

class _ClockInButtonState extends ConsumerState<ClockInButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rippleController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rippleAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rippleController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final timeTrackingState = ref.watch(timeTrackingProvider);
    final isWorking = timeTrackingState.isWorking;
    final canClockIn = timeTrackingState.canClockIn;
    final isLoading = timeTrackingState.isLoading;
    final currentDuration = ref.watch(currentWorkingDurationProvider);

    // Stop pulse animation when working or can't clock in
    if (isWorking || !canClockIn) {
      _pulseController.stop();
    } else {
      _pulseController.repeat(reverse: true);
    }

    return Column(
      children: [
        // Main Clock Button
        GestureDetector(
          onTapDown: (_) {
            if (canClockIn && !isLoading) {
              setState(() => _isPressed = true);
              _scaleController.forward();
              _rippleController.forward();
              HapticFeedback.mediumImpact();
            }
          },
          onTapUp: (_) {
            if (canClockIn && !isLoading) {
              setState(() => _isPressed = false);
              _scaleController.reverse();
              _rippleController.reverse();
            }
          },
          onTapCancel: () {
            setState(() => _isPressed = false);
            _scaleController.reverse();
            _rippleController.reverse();
          },
          onTap: (canClockIn && !isLoading) ? _handleClockAction : null,
          child: AnimatedBuilder(
            animation: Listenable.merge([
              _pulseAnimation,
              _rippleAnimation,
              _scaleAnimation,
            ]),
            builder: (context, child) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  // Ripple Effect
                  if (_isPressed)
                    Container(
                      width: 200 + (50 * _rippleAnimation.value),
                      height: 200 + (50 * _rippleAnimation.value),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: (isWorking ? AppTheme.errorColor : AppTheme.accentColor)
                            .withOpacity(0.2 * (1 - _rippleAnimation.value)),
                      ),
                    ),

                  // Pulse Ring (only when can clock in and not working)
                  if (canClockIn && !isWorking && !isLoading)
                    Container(
                      width: 180 * _pulseAnimation.value,
                      height: 180 * _pulseAnimation.value,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.accentColor.withOpacity(0.4),
                          width: 2,
                        ),
                      ),
                    ),

                  // Secondary pulse ring
                  if (canClockIn && !isWorking && !isLoading)
                    Container(
                      width: 200 * _pulseAnimation.value,
                      height: 200 * _pulseAnimation.value,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.accentColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),

                  // Main Button
                  Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      width: 160,
                      height: 160,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: _getButtonGradient(isWorking, canClockIn),
                        boxShadow: _getButtonShadow(isWorking, canClockIn, isLoading),
                      ),
                      child: _buildButtonContent(isWorking, canClockIn, isLoading, localizations),
                    ),
                  ),

                  // Loading Overlay
                  if (isLoading)
                    Container(
                      width: 160,
                      height: 160,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black.withOpacity(0.3),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Status Text
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _buildStatusText(canClockIn, isWorking, localizations),
        ),

        // Working Duration (if currently working)
        if (isWorking && currentDuration != null)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: _buildWorkingDurationDisplay(currentDuration),
          ),
      ],
    );
  }

  LinearGradient _getButtonGradient(bool isWorking, bool canClockIn) {
    if (!canClockIn) {
      return LinearGradient(
        colors: [
          Colors.grey.shade400,
          Colors.grey.shade500,
        ],
      );
    }

    if (isWorking) {
      return LinearGradient(
        colors: [
          AppTheme.errorColor,
          AppTheme.errorColor.withOpacity(0.8),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }

    return AppTheme.successGradient;
  }

  List<BoxShadow> _getButtonShadow(bool isWorking, bool canClockIn, bool isLoading) {
    if (!canClockIn || isLoading) return [];

    final color = isWorking ? AppTheme.errorColor : AppTheme.accentColor;
    return [
      BoxShadow(
        color: color.withOpacity(0.4),
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
      BoxShadow(
        color: color.withOpacity(0.2),
        blurRadius: 40,
        offset: const Offset(0, 16),
      ),
    ];
  }

  Widget _buildButtonContent(bool isWorking, bool canClockIn, bool isLoading, AppLocalizations localizations) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Icon with animation
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Icon(
            isWorking ? FontAwesomeIcons.stop : FontAwesomeIcons.play,
            key: ValueKey(isWorking),
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        // Text
        Text(
          isWorking ? localizations.clockOut : localizations.clockIn,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusText(bool canClockIn, bool isWorking, AppLocalizations localizations) {
    if (isWorking) {
      return Container(
        key: const ValueKey('working'),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.clock,
              size: 16,
              color: AppTheme.accentColor,
            ),
            const SizedBox(width: 8),
            Text(
              'Currently Working',
              style: TextStyle(
                color: AppTheme.accentColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (!canClockIn) {
      return Container(
        key: const ValueKey('not_at_worksite'),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.warningColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppTheme.warningColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.locationDot,
              size: 16,
              color: AppTheme.warningColor,
            ),
            const SizedBox(width: 8),
            Text(
              localizations.notAtWorksite,
              style: TextStyle(
                color: AppTheme.warningColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      key: const ValueKey('ready'),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.accentColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FontAwesomeIcons.check,
            size: 16,
            color: AppTheme.accentColor,
          ),
          const SizedBox(width: 8),
          Text(
            'Ready to Clock In',
            style: TextStyle(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingDurationDisplay(Duration duration) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FontAwesomeIcons.stopwatch,
            size: 16,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            _formatDuration(duration),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleClockAction() async {
    final timeTrackingNotifier = ref.read(timeTrackingProvider.notifier);
    final isWorking = ref.read(timeTrackingProvider).isWorking;

    try {
      HapticFeedback.heavyImpact();
      
      if (isWorking) {
        await timeTrackingNotifier.clockOut();
        if (mounted) {
          _showSuccessMessage('Clocked out successfully');
        }
      } else {
        await timeTrackingNotifier.clockIn();
        if (mounted) {
          _showSuccessMessage('Clocked in successfully');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage(e.toString());
      }
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(FontAwesomeIcons.check, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: AppTheme.accentColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(FontAwesomeIcons.exclamationTriangle, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    return '${hours.toString().padLeft(2, '0')}:'
           '${minutes.toString().padLeft(2, '0')}:'
           '${seconds.toString().padLeft(2, '0')}';
  }
}
