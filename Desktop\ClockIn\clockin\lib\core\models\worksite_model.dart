import 'package:hive/hive.dart';
import 'dart:math';

part 'worksite_model.g.dart';

@HiveType(typeId: 2)
class WorksiteModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final double latitude;

  @HiveField(4)
  final double longitude;

  @HiveField(5)
  final double radius;

  @HiveField(6)
  final String? address;

  @HiveField(7)
  final WorksiteType type;

  @HiveField(8)
  final bool isActive;

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final String? createdBy;

  WorksiteModel({
    required this.id,
    required this.name,
    this.description = '',
    required this.latitude,
    required this.longitude,
    this.radius = 50.0,
    this.address,
    this.type = WorksiteType.construction,
    this.isActive = true,
    required this.createdAt,
    this.createdBy,
  });

  factory WorksiteModel.fromJson(Map<String, dynamic> json) {
    return WorksiteModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      radius: (json['radius'] as num?)?.toDouble() ?? 50.0,
      address: json['address'] as String?,
      type: WorksiteType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => WorksiteType.construction,
      ),
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'radius': radius,
      'address': address,
      'type': type.toString().split('.').last,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  /// Calculate distance from given coordinates to this worksite
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371000; // Earth's radius in meters
    
    final double lat1Rad = latitude * (pi / 180);
    final double lat2Rad = lat * (pi / 180);
    final double deltaLatRad = (lat - latitude) * (pi / 180);
    final double deltaLngRad = (lng - longitude) * (pi / 180);

    final double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  /// Check if given coordinates are within the worksite radius
  bool isWithinRadius(double lat, double lng) {
    final double distance = distanceFrom(lat, lng);
    return distance <= radius;
  }

  WorksiteModel copyWith({
    String? id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    double? radius,
    String? address,
    WorksiteType? type,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
  }) {
    return WorksiteModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      radius: radius ?? this.radius,
      address: address ?? this.address,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorksiteModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'WorksiteModel(id: $id, name: $name, latitude: $latitude, longitude: $longitude)';
  }
}

@HiveType(typeId: 3)
enum WorksiteType {
  @HiveField(0)
  construction,

  @HiveField(1)
  office,

  @HiveField(2)
  warehouse,

  @HiveField(3)
  factory,

  @HiveField(4)
  retail,

  @HiveField(5)
  other,
}
