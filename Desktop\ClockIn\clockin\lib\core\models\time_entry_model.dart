import 'package:hive/hive.dart';

part 'time_entry_model.g.dart';

@HiveType(typeId: 4)
class TimeEntryModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final String worksiteId;

  @HiveField(3)
  final DateTime clockInTime;

  @HiveField(4)
  final DateTime? clockOutTime;

  @HiveField(5)
  final LocationData clockInLocation;

  @HiveField(6)
  final LocationData? clockOutLocation;

  @HiveField(7)
  final Duration? totalDuration;

  @HiveField(8)
  final TimeEntryStatus status;

  @HiveField(9)
  final String? notes;

  @HiveField(10)
  final String? deviceFingerprint;

  @HiveField(11)
  final DateTime createdAt;

  TimeEntryModel({
    required this.id,
    required this.userId,
    required this.worksiteId,
    required this.clockInTime,
    this.clockOutTime,
    required this.clockInLocation,
    this.clockOutLocation,
    this.totalDuration,
    this.status = TimeEntryStatus.active,
    this.notes,
    this.deviceFingerprint,
    required this.createdAt,
  });

  factory TimeEntryModel.fromJson(Map<String, dynamic> json) {
    return TimeEntryModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      worksiteId: json['worksite_id'] as String,
      clockInTime: DateTime.parse(json['clock_in_time'] as String),
      clockOutTime: json['clock_out_time'] != null
          ? DateTime.parse(json['clock_out_time'] as String)
          : null,
      clockInLocation: LocationData.fromJson(json['clock_in_location'] as Map<String, dynamic>),
      clockOutLocation: json['clock_out_location'] != null
          ? LocationData.fromJson(json['clock_out_location'] as Map<String, dynamic>)
          : null,
      totalDuration: json['total_duration'] != null
          ? Duration(seconds: json['total_duration'] as int)
          : null,
      status: TimeEntryStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TimeEntryStatus.active,
      ),
      notes: json['notes'] as String?,
      deviceFingerprint: json['device_fingerprint'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'worksite_id': worksiteId,
      'clock_in_time': clockInTime.toIso8601String(),
      'clock_out_time': clockOutTime?.toIso8601String(),
      'clock_in_location': clockInLocation.toJson(),
      'clock_out_location': clockOutLocation?.toJson(),
      'total_duration': totalDuration?.inSeconds,
      'status': status.toString().split('.').last,
      'notes': notes,
      'device_fingerprint': deviceFingerprint,
      'created_at': createdAt.toIso8601String(),
    };
  }

  TimeEntryModel copyWith({
    String? id,
    String? userId,
    String? worksiteId,
    DateTime? clockInTime,
    DateTime? clockOutTime,
    LocationData? clockInLocation,
    LocationData? clockOutLocation,
    Duration? totalDuration,
    TimeEntryStatus? status,
    String? notes,
    String? deviceFingerprint,
    DateTime? createdAt,
  }) {
    return TimeEntryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      worksiteId: worksiteId ?? this.worksiteId,
      clockInTime: clockInTime ?? this.clockInTime,
      clockOutTime: clockOutTime ?? this.clockOutTime,
      clockInLocation: clockInLocation ?? this.clockInLocation,
      clockOutLocation: clockOutLocation ?? this.clockOutLocation,
      totalDuration: totalDuration ?? this.totalDuration,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      deviceFingerprint: deviceFingerprint ?? this.deviceFingerprint,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeEntryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TimeEntryModel(id: $id, userId: $userId, status: $status)';
  }
}

@HiveType(typeId: 5)
class LocationData extends HiveObject {
  @HiveField(0)
  final double latitude;

  @HiveField(1)
  final double longitude;

  @HiveField(2)
  final double accuracy;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final String? address;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
    this.address,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      address: json['address'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp.toIso8601String(),
      'address': address,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode ^ timestamp.hashCode;

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, accuracy: $accuracy)';
  }
}

@HiveType(typeId: 6)
enum TimeEntryStatus {
  @HiveField(0)
  active,

  @HiveField(1)
  completed,

  @HiveField(2)
  cancelled,
}
