import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'api_service.dart';

class EmployeeService {
  static final EmployeeService _instance = EmployeeService._internal();
  factory EmployeeService() => _instance;
  EmployeeService._internal();

  final ApiService _apiService = ApiService();

  /// Get list of employees (admin only)
  Future<List<UserModel>> getEmployees({
    String? search,
    int? page,
    int? perPage,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final response = await _apiService.get(
        '/employees',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => UserModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get employees: $e');
      return [];
    }
  }

  /// Get employee by ID (admin only)
  Future<UserModel?> getEmployeeById(String id) async {
    try {
      final response = await _apiService.get('/employees/$id');

      if (response['success'] == true) {
        return UserModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get employee by ID: $e');
      return null;
    }
  }

  /// Create new employee (admin only)
  Future<UserModel?> createEmployee({
    required String name,
    required String email,
    required String password,
    required String role,
    String? phoneNumber,
    String? department,
  }) async {
    try {
      final data = {
        'name': name,
        'email': email,
        'password': password,
        'role': role,
        if (phoneNumber != null) 'phone_number': phoneNumber,
        if (department != null) 'department': department,
      };

      final response = await _apiService.post('/employees', data);

      if (response['success'] == true) {
        return UserModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to create employee: $e');
      return null;
    }
  }

  /// Update employee (admin only)
  Future<UserModel?> updateEmployee(
    String id, {
    String? name,
    String? email,
    String? password,
    String? role,
    String? phoneNumber,
    String? department,
    bool? isActive,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (email != null) data['email'] = email;
      if (password != null) data['password'] = password;
      if (role != null) data['role'] = role;
      if (phoneNumber != null) data['phone_number'] = phoneNumber;
      if (department != null) data['department'] = department;
      if (isActive != null) data['is_active'] = isActive;

      final response = await _apiService.put('/employees/$id', data);

      if (response['success'] == true) {
        return UserModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to update employee: $e');
      return null;
    }
  }

  /// Delete employee (admin only)
  Future<bool> deleteEmployee(String id) async {
    try {
      final response = await _apiService.delete('/employees/$id');
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to delete employee: $e');
      return false;
    }
  }

  /// Request location verification from employee (admin only)
  Future<bool> requestLocationVerification({
    required String userId,
    required String message,
  }) async {
    try {
      final data = {
        'user_id': userId,
        'message': message,
      };

      final response = await _apiService.post('/notifications/request-verification', data);
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to request location verification: $e');
      return false;
    }
  }

  /// Assign worksite to employees (admin only)
  Future<bool> assignWorksite({
    required String worksiteId,
    required List<String> employeeIds,
    required String message,
  }) async {
    try {
      final data = {
        'worksite_id': worksiteId,
        'employee_ids': employeeIds,
        'message': message,
      };

      final response = await _apiService.post('/notifications/assign-site', data);
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to assign worksite: $e');
      return false;
    }
  }

  /// Get employee time tracking statistics
  Future<Map<String, dynamic>?> getEmployeeStats(String employeeId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _apiService.get(
        '/employees/$employeeId/stats',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        return response['data'];
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get employee stats: $e');
      return null;
    }
  }

  /// Get employee time entries
  Future<List<Map<String, dynamic>>> getEmployeeTimeEntries(
    String employeeId, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();
      if (limit != null) queryParams['limit'] = limit.toString();

      final response = await _apiService.get(
        '/employees/$employeeId/time-entries',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        return List<Map<String, dynamic>>.from(response['data']);
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get employee time entries: $e');
      return [];
    }
  }

  /// Update FCM token for notifications
  Future<bool> updateFcmToken(String token) async {
    try {
      final data = {'fcm_token': token};
      final response = await _apiService.post('/notifications/update-fcm-token', data);
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to update FCM token: $e');
      return false;
    }
  }

  /// Get notification history
  Future<List<Map<String, dynamic>>> getNotificationHistory({
    int? page,
    int? perPage,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final response = await _apiService.get(
        '/notifications/history',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        return List<Map<String, dynamic>>.from(response['data']);
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get notification history: $e');
      return [];
    }
  }
}
