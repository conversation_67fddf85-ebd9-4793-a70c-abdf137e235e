/// Utility class for handling multilingual messages from the API
class MessageUtils {
  /// Extract message from multilingual response
  /// Tries to get message in preferred language order: fr, en, ar
  static String extractMessage(dynamic message, {String defaultMessage = 'Unknown error'}) {
    if (message == null) return defaultMessage;
    
    if (message is String) return message;
    
    if (message is Map<String, dynamic>) {
      // Try to get message in preferred language order: fr, en, ar
      return message['fr'] ?? message['en'] ?? message['ar'] ?? defaultMessage;
    }
    
    return defaultMessage;
  }

  /// Extract success message with default
  static String extractSuccessMessage(dynamic message) {
    return extractMessage(message, defaultMessage: 'Operation completed successfully');
  }

  /// Extract error message with default
  static String extractErrorMessage(dynamic message) {
    return extractMessage(message, defaultMessage: 'An error occurred');
  }

  /// Check if response is successful
  static bool isSuccessResponse(Map<String, dynamic> response) {
    return response['success'] == true;
  }

  /// Get data from response safely
  static T? getResponseData<T>(Map<String, dynamic> response) {
    if (isSuccessResponse(response)) {
      return response['data'] as T?;
    }
    return null;
  }

  /// Get error message from response
  static String getErrorMessage(Map<String, dynamic> response) {
    return extractErrorMessage(response['message']);
  }

  /// Get success message from response
  static String getSuccessMessage(Map<String, dynamic> response) {
    return extractSuccessMessage(response['message']);
  }
}
