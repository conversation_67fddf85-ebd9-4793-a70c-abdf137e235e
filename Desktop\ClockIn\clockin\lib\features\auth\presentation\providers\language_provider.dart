import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';

// Language State
class LanguageState {
  final Locale locale;
  final TextDirection textDirection;

  const LanguageState({
    required this.locale,
    required this.textDirection,
  });

  LanguageState copyWith({
    Locale? locale,
    TextDirection? textDirection,
  }) {
    return LanguageState(
      locale: locale ?? this.locale,
      textDirection: textDirection ?? this.textDirection,
    );
  }
}

// Language Notifier
class LanguageNotifier extends StateNotifier<LanguageState> {
  LanguageNotifier() : super(const LanguageState(
    locale: Locale('en', 'US'),
    textDirection: TextDirection.ltr,
  )) {
    _loadSavedLanguage();
  }

  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(AppConstants.languageKey) ?? 'en';
      await changeLanguage(languageCode);
    } catch (e) {
      // Use default language if loading fails
    }
  }

  Future<void> changeLanguage(String languageCode) async {
    Locale newLocale;
    TextDirection newDirection;

    switch (languageCode) {
      case 'ar':
        newLocale = const Locale('ar', 'SA');
        newDirection = TextDirection.rtl;
        break;
      case 'fr':
        newLocale = const Locale('fr', 'FR');
        newDirection = TextDirection.ltr;
        break;
      case 'en':
      default:
        newLocale = const Locale('en', 'US');
        newDirection = TextDirection.ltr;
        break;
    }

    state = state.copyWith(
      locale: newLocale,
      textDirection: newDirection,
    );

    // Save to preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.languageKey, languageCode);
    } catch (e) {
      // Handle error silently
    }
  }

  String get currentLanguageCode => state.locale.languageCode;
  
  bool get isRTL => state.textDirection == TextDirection.rtl;
  
  List<LanguageOption> get availableLanguages => [
    LanguageOption(
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
    ),
    LanguageOption(
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
    ),
    LanguageOption(
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
    ),
  ];
}

// Language Option Model
class LanguageOption {
  final String code;
  final String name;
  final String nativeName;
  final String flag;

  LanguageOption({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.flag,
  });
}

// Providers
final languageProvider = StateNotifierProvider<LanguageNotifier, LanguageState>((ref) {
  return LanguageNotifier();
});

// Computed providers
final localeProvider = Provider<Locale>((ref) {
  return ref.watch(languageProvider).locale;
});

final textDirectionProvider = Provider<TextDirection>((ref) {
  return ref.watch(languageProvider).textDirection;
});

final isRTLProvider = Provider<bool>((ref) {
  return ref.watch(languageProvider).textDirection == TextDirection.rtl;
});

final currentLanguageCodeProvider = Provider<String>((ref) {
  return ref.watch(languageProvider).locale.languageCode;
});

final availableLanguagesProvider = Provider<List<LanguageOption>>((ref) {
  return ref.watch(languageProvider.notifier).availableLanguages;
});
