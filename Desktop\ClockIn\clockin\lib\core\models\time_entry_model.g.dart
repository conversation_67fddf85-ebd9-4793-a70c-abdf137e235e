// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'time_entry_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TimeEntryModelAdapter extends TypeAdapter<TimeEntryModel> {
  @override
  final int typeId = 4;

  @override
  TimeEntryModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TimeEntryModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      worksiteId: fields[2] as String,
      clockInTime: fields[3] as DateTime,
      clockOutTime: fields[4] as DateTime?,
      clockInLocation: fields[5] as LocationData,
      clockOutLocation: fields[6] as LocationData?,
      totalDuration: fields[7] as Duration?,
      status: fields[8] as TimeEntryStatus,
      notes: fields[9] as String?,
      deviceFingerprint: fields[10] as String?,
      createdAt: fields[11] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, TimeEntryModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.worksiteId)
      ..writeByte(3)
      ..write(obj.clockInTime)
      ..writeByte(4)
      ..write(obj.clockOutTime)
      ..writeByte(5)
      ..write(obj.clockInLocation)
      ..writeByte(6)
      ..write(obj.clockOutLocation)
      ..writeByte(7)
      ..write(obj.totalDuration)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.notes)
      ..writeByte(10)
      ..write(obj.deviceFingerprint)
      ..writeByte(11)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeEntryModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocationDataAdapter extends TypeAdapter<LocationData> {
  @override
  final int typeId = 5;

  @override
  LocationData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationData(
      latitude: fields[0] as double,
      longitude: fields[1] as double,
      accuracy: fields[2] as double,
      timestamp: fields[3] as DateTime,
      address: fields[4] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, LocationData obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude)
      ..writeByte(2)
      ..write(obj.accuracy)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.address);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TimeEntryStatusAdapter extends TypeAdapter<TimeEntryStatus> {
  @override
  final int typeId = 6;

  @override
  TimeEntryStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TimeEntryStatus.active;
      case 1:
        return TimeEntryStatus.completed;
      case 2:
        return TimeEntryStatus.cancelled;
      default:
        return TimeEntryStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, TimeEntryStatus obj) {
    switch (obj) {
      case TimeEntryStatus.active:
        writer.writeByte(0);
        break;
      case TimeEntryStatus.completed:
        writer.writeByte(1);
        break;
      case TimeEntryStatus.cancelled:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeEntryStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
