# Guide d'Installation ClockIn

## Prérequis

### Environnement de Développement
- **Flutter SDK** : Version 3.7.2 ou supérieure
- **Dart SDK** : Inclus avec Flutter
- **Android Studio** : Pour le développement Android
- **Xcode** : Pour le développement iOS (macOS uniquement)
- **VS Code** : Éditeur recommandé avec extensions Flutter/Dart

### Backend (Laravel + WampServer)
- **WampServer** : Version 3.2.0 ou supérieure
- **PHP** : Version 8.1 ou supérieure
- **MySQL** : Version 8.0 ou supérieure
- **Composer** : Gestionnaire de dépendances PHP

### Services Externes
- **Compte Firebase** : Pour l'authentification et les notifications push
- **Google Maps API** : Pour les cartes et géolocalisation

## Installation Flutter

### 1. Installation de Flutter

#### Windows
```bash
# Télécharger Flutter SDK depuis https://flutter.dev
# Extraire dans C:\flutter
# Ajouter C:\flutter\bin au PATH

# Vérifier l'installation
flutter doctor
```

#### macOS
```bash
# Installer via Homebrew
brew install flutter

# Ou télécharger depuis https://flutter.dev
# Ajouter au PATH dans ~/.zshrc ou ~/.bash_profile
export PATH="$PATH:/path/to/flutter/bin"
```

### 2. Configuration de l'Environnement

```bash
# Accepter les licences Android
flutter doctor --android-licenses

# Vérifier la configuration
flutter doctor -v
```

### 3. Installation du Projet

```bash
# Cloner le projet
git clone <repository-url>
cd clockin

# Installer les dépendances
flutter pub get

# Générer les adaptateurs Hive
flutter packages pub run build_runner build

# Nettoyer et reconstruire si nécessaire
flutter clean
flutter pub get
```

## Configuration Backend (Laravel)

### 1. Installation de WampServer

1. Télécharger WampServer depuis [wampserver.com](http://wampserver.com)
2. Installer avec les paramètres par défaut
3. Démarrer tous les services (Apache, MySQL, PHP)

### 2. Création du Projet Laravel

```bash
# Naviguer vers le dossier www de WampServer
cd C:\wamp64\www

# Créer un nouveau projet Laravel
composer create-project laravel/laravel clockin-api

# Naviguer dans le projet
cd clockin-api
```

### 3. Configuration de la Base de Données

#### Créer la base de données
1. Ouvrir phpMyAdmin : `http://localhost/phpmyadmin`
2. Créer une nouvelle base de données : `clockin_db`
3. Utiliser l'encodage : `utf8mb4_unicode_ci`

#### Configuration .env
```env
APP_NAME=ClockIn-API
APP_ENV=local
APP_KEY=base64:generated_key_here
APP_DEBUG=true
APP_URL=http://localhost/clockin-api/public

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=clockin_db
DB_USERNAME=root
DB_PASSWORD=

JWT_SECRET=your_jwt_secret_here

FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY="your_firebase_private_key"
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
```

### 4. Installation des Dépendances Laravel

```bash
# Installer JWT Auth
composer require tymon/jwt-auth

# Installer Firebase Admin SDK
composer require kreait/firebase-php

# Installer CORS
composer require fruitcake/laravel-cors

# Publier les configurations
php artisan vendor:publish --provider="Tymon\JWTAuth\Providers\LaravelServiceProvider"
php artisan vendor:publish --tag="cors"

# Générer la clé JWT
php artisan jwt:secret
```

### 5. Migrations et Seeders

```bash
# Créer les migrations
php artisan make:migration create_users_table
php artisan make:migration create_worksites_table
php artisan make:migration create_time_entries_table
php artisan make:migration create_security_logs_table

# Exécuter les migrations
php artisan migrate

# Créer et exécuter les seeders
php artisan make:seeder UserSeeder
php artisan make:seeder WorksiteSeeder
php artisan db:seed
```

## Configuration Firebase

### 1. Création du Projet Firebase

1. Aller sur [Firebase Console](https://console.firebase.google.com)
2. Créer un nouveau projet : "ClockIn"
3. Activer l'authentification et Cloud Messaging

### 2. Configuration Android

1. Ajouter une app Android dans Firebase
2. Package name : `com.example.clockin`
3. Télécharger `google-services.json`
4. Placer dans `android/app/`

### 3. Configuration iOS

1. Ajouter une app iOS dans Firebase
2. Bundle ID : `com.example.clockin`
3. Télécharger `GoogleService-Info.plist`
4. Placer dans `ios/Runner/`

### 4. Configuration Flutter

```yaml
# pubspec.yaml - Déjà configuré dans le projet
dependencies:
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  firebase_messaging: ^15.1.3
```

## Configuration des API Keys

### 1. Google Maps API

1. Aller sur [Google Cloud Console](https://console.cloud.google.com)
2. Créer un projet ou utiliser un existant
3. Activer les APIs :
   - Maps SDK for Android
   - Maps SDK for iOS
   - Geocoding API
4. Créer une clé API

#### Configuration Android
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_GOOGLE_MAPS_API_KEY" />
```

#### Configuration iOS
```xml
<!-- ios/Runner/AppDelegate.swift -->
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("YOUR_GOOGLE_MAPS_API_KEY")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

## Configuration de l'Application

### 1. Configuration des URLs API

```dart
// lib/core/constants/app_constants.dart
class AppConstants {
  static const String baseUrl = 'http://********/clockin-api/public/api'; // Android Emulator
  // static const String baseUrl = 'http://localhost/clockin-api/public/api'; // iOS Simulator
  // static const String baseUrl = 'http://YOUR_IP/clockin-api/public/api'; // Device physique
}
```

### 2. Configuration des Permissions

Les permissions sont déjà configurées dans :
- `android/app/src/main/AndroidManifest.xml`
- `ios/Runner/Info.plist`

## Tests et Déploiement

### 1. Tests de l'Application

```bash
# Tests unitaires
flutter test

# Tests d'intégration
flutter test integration_test/

# Analyser le code
flutter analyze
```

### 2. Build de l'Application

#### Android
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (recommandé pour Play Store)
flutter build appbundle --release
```

#### iOS
```bash
# Debug
flutter build ios --debug

# Release
flutter build ios --release
```

### 3. Tests sur Appareils

#### Android
```bash
# Lister les appareils connectés
flutter devices

# Lancer sur un appareil spécifique
flutter run -d <device-id>
```

#### iOS
```bash
# Ouvrir le projet iOS dans Xcode
open ios/Runner.xcworkspace

# Ou lancer directement
flutter run -d <device-id>
```

## Dépannage

### Problèmes Courants

#### Flutter Doctor Issues
```bash
# Problème de licences Android
flutter doctor --android-licenses

# Problème de certificats iOS
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer
```

#### Problèmes de Build
```bash
# Nettoyer le cache
flutter clean
flutter pub get

# Problèmes iOS
cd ios && pod install && cd ..
```

#### Problèmes de Géolocalisation
- Vérifier les permissions dans les manifestes
- Tester sur un appareil physique (pas d'émulateur)
- Vérifier que le GPS est activé

#### Problèmes d'API
- Vérifier que WampServer est démarré
- Tester les endpoints avec Postman
- Vérifier les logs Laravel : `storage/logs/laravel.log`

### Logs et Debugging

#### Flutter
```bash
# Logs en temps réel
flutter logs

# Debug avec breakpoints
flutter run --debug
```

#### Laravel
```bash
# Logs Laravel
tail -f storage/logs/laravel.log

# Mode debug
APP_DEBUG=true dans .env
```

## Support et Documentation

### Ressources Utiles
- [Documentation Flutter](https://flutter.dev/docs)
- [Documentation Laravel](https://laravel.com/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Google Maps Flutter Plugin](https://pub.dev/packages/google_maps_flutter)

### Contact Support
- Créer une issue sur GitHub
- Consulter la documentation API
- Vérifier les logs d'erreur

---

**Note** : Ce guide suppose une installation sur un environnement de développement local. Pour un déploiement en production, des configurations supplémentaires de sécurité et de performance sont nécessaires.
