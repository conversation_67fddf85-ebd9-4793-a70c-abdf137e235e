import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/time_entry_model.dart';
import '../models/worksite_model.dart';
import '../constants/app_constants.dart';
import 'security_service.dart';
import 'api_service.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  StreamController<Position>? _positionStreamController;
  StreamSubscription<Position>? _positionStreamSubscription;
  Timer? _continuousVerificationTimer;
  Position? _lastKnownPosition;
  WorksiteModel? _currentWorksite;
  bool _isTrackingActive = false;

  Position? get lastKnownPosition => _lastKnownPosition;
  Stream<Position>? get positionStream => _positionStreamController?.stream;
  bool get isTrackingActive => _isTrackingActive;
  WorksiteModel? get currentWorksite => _currentWorksite;

  Future<LocationPermissionResult> requestLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionResult.serviceDisabled;
      }

      // Check current permission status
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return LocationPermissionResult.denied;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationPermissionResult.deniedForever;
      }

      // Request background location permission for continuous tracking
      final backgroundPermission = await Permission.locationAlways.request();
      if (backgroundPermission.isDenied) {
        // Fall back to when-in-use permission
        final whenInUsePermission = await Permission.locationWhenInUse.request();
        if (whenInUsePermission.isDenied) {
          return LocationPermissionResult.denied;
        }
      }

      return LocationPermissionResult.granted;
    } catch (e) {
      return LocationPermissionResult.error;
    }
  }

  Future<LocationData?> getCurrentLocation() async {
    try {
      final permissionResult = await requestLocationPermission();
      if (permissionResult != LocationPermissionResult.granted) {
        throw LocationException('Location permission not granted');
      }

      // Perform security check
      final securityService = SecurityService();
      final isMocked = await securityService.isMockLocationEnabled();
      if (isMocked) {
        throw LocationException('Mock location detected. Please disable fake GPS apps.');
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      // Validate location
      final validationResult = await securityService.validateLocation(position);
      if (!validationResult.isValid) {
        throw LocationException('Location validation failed: ${validationResult.reason}');
      }

      _lastKnownPosition = position;

      // Get address from coordinates
      String? address;
      try {
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );
        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          address = '${placemark.street}, ${placemark.locality}, ${placemark.country}';
        }
      } catch (e) {
        // Address lookup failed, continue without it
      }

      return LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: DateTime.now(),
        address: address,
      );
    } catch (e) {
      throw LocationException('Failed to get current location: ${e.toString()}');
    }
  }

  Future<void> startLocationTracking() async {
    try {
      final permissionResult = await requestLocationPermission();
      if (permissionResult != LocationPermissionResult.granted) {
        throw LocationException('Location permission not granted');
      }

      _positionStreamController = StreamController<Position>.broadcast();
      _isTrackingActive = true;

      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5, // Update every 5 meters
      );

      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) async {
          // Validate each position update
          final securityService = SecurityService();
          final validationResult = await securityService.validateLocation(position);
          
          if (validationResult.isValid) {
            _lastKnownPosition = position;
            _positionStreamController?.add(position);
          } else {
            // Log security violation
            await _logSecurityViolation('Invalid location: ${validationResult.reason}');
          }
        },
        onError: (error) {
          _positionStreamController?.addError(error);
        },
      );

      // Start continuous verification timer
      _startContinuousVerification();
    } catch (e) {
      _isTrackingActive = false;
      throw LocationException('Failed to start location tracking: ${e.toString()}');
    }
  }

  void _startContinuousVerification() {
    _continuousVerificationTimer?.cancel();
    _continuousVerificationTimer = Timer.periodic(
      Duration(minutes: AppConstants.locationUpdateInterval),
      (timer) async {
        await _performContinuousVerification();
      },
    );
  }

  Future<void> _performContinuousVerification() async {
    try {
      if (!_isTrackingActive || _currentWorksite == null) return;

      final currentLocation = await getCurrentLocation();
      if (currentLocation == null) {
        await _logSecurityViolation('Failed to get location during continuous verification');
        return;
      }

      final isWithinRadius = _currentWorksite!.isWithinRadius(
        currentLocation.latitude,
        currentLocation.longitude,
      );

      if (!isWithinRadius) {
        await _logSecurityViolation('Employee moved outside worksite radius');
        // Notify admin or take appropriate action
        await _notifyLocationViolation();
      }

      // Check for mock location
      final securityService = SecurityService();
      final isMocked = await securityService.isMockLocationEnabled();
      if (isMocked) {
        await _logSecurityViolation('Mock location detected during continuous verification');
        await _notifySecurityViolation('Mock location detected');
      }

      // Log verification success
      await _logLocationVerification(currentLocation, isWithinRadius);
    } catch (e) {
      await _logSecurityViolation('Continuous verification failed: $e');
    }
  }

  Future<void> stopLocationTracking() async {
    _isTrackingActive = false;
    _continuousVerificationTimer?.cancel();
    await _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    await _positionStreamController?.close();
    _positionStreamController = null;
    _currentWorksite = null;
  }

  Future<bool> isWithinWorksite(WorksiteModel worksite) async {
    try {
      final currentLocation = await getCurrentLocation();
      if (currentLocation == null) return false;

      return worksite.isWithinRadius(
        currentLocation.latitude,
        currentLocation.longitude,
      );
    } catch (e) {
      return false;
    }
  }

  Future<double> distanceToWorksite(WorksiteModel worksite) async {
    try {
      final currentLocation = await getCurrentLocation();
      if (currentLocation == null) return double.infinity;

      return worksite.distanceFrom(
        currentLocation.latitude,
        currentLocation.longitude,
      );
    } catch (e) {
      return double.infinity;
    }
  }

  Future<List<WorksiteModel>> getNearbyWorksites(
    List<WorksiteModel> allWorksites,
    {double maxDistance = 1000}
  ) async {
    try {
      final currentLocation = await getCurrentLocation();
      if (currentLocation == null) return [];

      final nearbyWorksites = <WorksiteModel>[];
      
      for (final worksite in allWorksites) {
        final distance = worksite.distanceFrom(
          currentLocation.latitude,
          currentLocation.longitude,
        );
        
        if (distance <= maxDistance) {
          nearbyWorksites.add(worksite);
        }
      }

      // Sort by distance
      nearbyWorksites.sort((a, b) {
        final distanceA = a.distanceFrom(
          currentLocation.latitude,
          currentLocation.longitude,
        );
        final distanceB = b.distanceFrom(
          currentLocation.latitude,
          currentLocation.longitude,
        );
        return distanceA.compareTo(distanceB);
      });

      return nearbyWorksites;
    } catch (e) {
      return [];
    }
  }

  Future<LocationCheckResult> checkLocationForClockIn(WorksiteModel worksite) async {
    try {
      // Perform comprehensive security check
      final securityService = SecurityService();
      final securityCheck = await securityService.performSecurityCheck();
      
      if (!securityCheck.isSecure) {
        return LocationCheckResult(
          canClockIn: false,
          reason: 'Security check failed: ${securityCheck.issues.join(', ')}',
          securityIssues: securityCheck.issues,
        );
      }

      // Check if location services are enabled
      if (!await Geolocator.isLocationServiceEnabled()) {
        return LocationCheckResult(
          canClockIn: false,
          reason: 'Location services are disabled. Please enable GPS.',
        );
      }

      // Get current location with high accuracy
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
        timeLimit: const Duration(seconds: 15),
      );

      // Validate location
      final validationResult = await securityService.validateLocation(position);
      if (!validationResult.isValid) {
        return LocationCheckResult(
          canClockIn: false,
          reason: 'Location validation failed: ${validationResult.reason}',
        );
      }

      // Check if within worksite radius
      final distance = worksite.distanceFrom(position.latitude, position.longitude);
      final isWithinRadius = distance <= worksite.radius;

      if (!isWithinRadius) {
        return LocationCheckResult(
          canClockIn: false,
          reason: 'You are ${formatDistance(distance)} away from the worksite. Please move closer.',
          distance: distance,
        );
      }

      // Set current worksite for tracking
      _currentWorksite = worksite;

      return LocationCheckResult(
        canClockIn: true,
        reason: 'Location verified. You can clock in.',
        distance: distance,
        location: LocationData(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      return LocationCheckResult(
        canClockIn: false,
        reason: 'Location check failed: ${e.toString()}',
      );
    }
  }

  Future<void> _logSecurityViolation(String violation) async {
    try {
      final apiService = ApiService();
      await apiService.post('/security/log-violation', {
        'violation': violation,
        'timestamp': DateTime.now().toIso8601String(),
        'location': _lastKnownPosition != null ? {
          'latitude': _lastKnownPosition!.latitude,
          'longitude': _lastKnownPosition!.longitude,
        } : null,
      });
    } catch (e) {
      // Log locally if API fails
      print('Failed to log security violation: $e');
    }
  }

  Future<void> _logLocationVerification(LocationData location, bool isValid) async {
    try {
      final apiService = ApiService();
      await apiService.post('/location/log-verification', {
        'location': location.toJson(),
        'is_valid': isValid,
        'worksite_id': _currentWorksite?.id,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Failed to log location verification: $e');
    }
  }

  Future<void> _notifyLocationViolation() async {
    // This would trigger a notification to admin
    // Implementation depends on notification service
  }

  Future<void> _notifySecurityViolation(String violation) async {
    // This would trigger an immediate security alert
    // Implementation depends on notification service
  }

  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  void dispose() {
    stopLocationTracking();
  }
}

class LocationCheckResult {
  final bool canClockIn;
  final String reason;
  final double? distance;
  final LocationData? location;
  final List<String>? securityIssues;

  LocationCheckResult({
    required this.canClockIn,
    required this.reason,
    this.distance,
    this.location,
    this.securityIssues,
  });
}

class LocationException implements Exception {
  final String message;
  LocationException(this.message);
  
  @override
  String toString() => 'LocationException: $message';
}

enum LocationPermissionResult {
  granted,
  denied,
  deniedForever,
  serviceDisabled,
  error,
}
