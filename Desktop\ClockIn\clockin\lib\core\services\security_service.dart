import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter/services.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// Check if the device is using mock location (fake GPS)
  Future<bool> isMockLocationEnabled() async {
    try {
      if (Platform.isAndroid) {
        return await _checkAndroidMockLocation();
      } else if (Platform.isIOS) {
        return await _checkiOSMockLocation();
      }
      return false;
    } catch (e) {
      // If we can't determine, assume it's not mocked for safety
      return false;
    }
  }

  Future<bool> _checkAndroidMockLocation() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      
      // Check if device is rooted (basic check)
      if (await _isAndroidRooted()) {
        return true; // Consider rooted devices as potentially compromised
      }

      // Check for mock location apps (this requires additional permissions)
      // For now, we'll use a basic position accuracy check
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // If accuracy is suspiciously perfect (0 or very low), it might be mocked
      if (position.accuracy <= 1.0) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _checkiOSMockLocation() async {
    try {
      final iosInfo = await _deviceInfo.iosInfo;
      
      // Check if device is jailbroken (basic check)
      if (await _isiOSJailbroken()) {
        return true;
      }

      // iOS has better protection against mock locations
      // We can check for simulator
      if (iosInfo.isPhysicalDevice == false) {
        return true; // Running on simulator
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _isAndroidRooted() async {
    try {
      // Check for common root indicators
      final List<String> rootPaths = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
        '/su/bin/su',
      ];

      for (String path in rootPaths) {
        if (await File(path).exists()) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _isiOSJailbroken() async {
    try {
      // Check for common jailbreak indicators
      final List<String> jailbreakPaths = [
        '/Applications/Cydia.app',
        '/Library/MobileSubstrate/MobileSubstrate.dylib',
        '/bin/bash',
        '/usr/sbin/sshd',
        '/etc/apt',
        '/private/var/lib/apt/',
        '/private/var/lib/cydia',
        '/private/var/mobile/Library/SBSettings/Themes',
        '/Library/MobileSubstrate/DynamicLibraries/Veency.plist',
        '/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist',
        '/System/Library/LaunchDaemons/com.ikey.bbot.plist',
        '/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist',
      ];

      for (String path in jailbreakPaths) {
        if (await File(path).exists()) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Validate location accuracy and consistency
  Future<LocationValidationResult> validateLocation(Position position) async {
    try {
      // Check accuracy
      if (position.accuracy > 100) {
        return LocationValidationResult(
          isValid: false,
          reason: 'Location accuracy too low (${position.accuracy}m)',
        );
      }

      // Check if coordinates are realistic
      if (position.latitude == 0.0 && position.longitude == 0.0) {
        return LocationValidationResult(
          isValid: false,
          reason: 'Invalid coordinates (0,0)',
        );
      }

      // Check timestamp
      final now = DateTime.now();
      final positionTime = position.timestamp ?? now;
      final timeDiff = now.difference(positionTime).abs();
      
      if (timeDiff.inMinutes > 5) {
        return LocationValidationResult(
          isValid: false,
          reason: 'Location timestamp too old',
        );
      }

      return LocationValidationResult(isValid: true);
    } catch (e) {
      return LocationValidationResult(
        isValid: false,
        reason: 'Location validation error: $e',
      );
    }
  }

  /// Get device security information
  Future<DeviceSecurityInfo> getDeviceSecurityInfo() async {
    try {
      final isMocked = await isMockLocationEnabled();
      final isLocationEnabled = await Geolocator.isLocationServiceEnabled();
      final locationPermission = await Geolocator.checkPermission();

      String deviceId = '';
      String deviceModel = '';
      String osVersion = '';
      bool isPhysicalDevice = true;

      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceId = androidInfo.id;
        deviceModel = androidInfo.model;
        osVersion = androidInfo.version.release;
        isPhysicalDevice = androidInfo.isPhysicalDevice;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
        deviceModel = iosInfo.model;
        osVersion = iosInfo.systemVersion;
        isPhysicalDevice = iosInfo.isPhysicalDevice;
      }

      return DeviceSecurityInfo(
        deviceId: deviceId,
        deviceModel: deviceModel,
        osVersion: osVersion,
        isPhysicalDevice: isPhysicalDevice,
        isMockLocationEnabled: isMocked,
        isLocationServiceEnabled: isLocationEnabled,
        locationPermission: locationPermission,
        isRooted: Platform.isAndroid ? await _isAndroidRooted() : false,
        isJailbroken: Platform.isIOS ? await _isiOSJailbroken() : false,
      );
    } catch (e) {
      throw SecurityException('Failed to get device security info: $e');
    }
  }

  /// Check if device meets security requirements
  Future<SecurityCheckResult> performSecurityCheck() async {
    try {
      final securityInfo = await getDeviceSecurityInfo();
      final issues = <String>[];

      if (!securityInfo.isPhysicalDevice) {
        issues.add('Running on emulator/simulator');
      }

      if (securityInfo.isRooted) {
        issues.add('Device is rooted');
      }

      if (securityInfo.isJailbroken) {
        issues.add('Device is jailbroken');
      }

      if (securityInfo.isMockLocationEnabled) {
        issues.add('Mock location detected');
      }

      if (!securityInfo.isLocationServiceEnabled) {
        issues.add('Location services disabled');
      }

      if (securityInfo.locationPermission == LocationPermission.denied ||
          securityInfo.locationPermission == LocationPermission.deniedForever) {
        issues.add('Location permission denied');
      }

      final isSecure = issues.isEmpty;
      final riskLevel = _calculateRiskLevel(issues.length);

      return SecurityCheckResult(
        isSecure: isSecure,
        riskLevel: riskLevel,
        issues: issues,
        deviceInfo: securityInfo,
      );
    } catch (e) {
      throw SecurityException('Security check failed: $e');
    }
  }

  SecurityRiskLevel _calculateRiskLevel(int issueCount) {
    if (issueCount == 0) return SecurityRiskLevel.low;
    if (issueCount <= 2) return SecurityRiskLevel.medium;
    return SecurityRiskLevel.high;
  }

  /// Generate device fingerprint for tracking
  Future<String> generateDeviceFingerprint() async {
    try {
      final securityInfo = await getDeviceSecurityInfo();
      final fingerprint = '${securityInfo.deviceId}_${securityInfo.deviceModel}_${securityInfo.osVersion}';
      return fingerprint.replaceAll(' ', '_').toLowerCase();
    } catch (e) {
      throw SecurityException('Failed to generate device fingerprint: $e');
    }
  }
}

class LocationValidationResult {
  final bool isValid;
  final String? reason;

  LocationValidationResult({required this.isValid, this.reason});
}

class DeviceSecurityInfo {
  final String deviceId;
  final String deviceModel;
  final String osVersion;
  final bool isPhysicalDevice;
  final bool isMockLocationEnabled;
  final bool isLocationServiceEnabled;
  final LocationPermission locationPermission;
  final bool isRooted;
  final bool isJailbroken;

  DeviceSecurityInfo({
    required this.deviceId,
    required this.deviceModel,
    required this.osVersion,
    required this.isPhysicalDevice,
    required this.isMockLocationEnabled,
    required this.isLocationServiceEnabled,
    required this.locationPermission,
    required this.isRooted,
    required this.isJailbroken,
  });
}

class SecurityCheckResult {
  final bool isSecure;
  final SecurityRiskLevel riskLevel;
  final List<String> issues;
  final DeviceSecurityInfo deviceInfo;

  SecurityCheckResult({
    required this.isSecure,
    required this.riskLevel,
    required this.issues,
    required this.deviceInfo,
  });
}

enum SecurityRiskLevel { low, medium, high }

class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);
  
  @override
  String toString() => 'SecurityException: $message';
}
