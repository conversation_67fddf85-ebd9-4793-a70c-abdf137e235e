# Guide d'Intégration API - ClockIn

## Vue d'ensemble

Ce guide explique comment l'application Flutter ClockIn se connecte au backend Lara<PERSON> et comment tester la connexion API.

## Configuration de Base

### 1. URL de l'API

L'URL de base de l'API est configurée dans `lib/core/constants/app_constants.dart` :

```dart
static const String baseUrl = 'http://localhost:8081/api';
```

**Options de configuration :**
- `http://localhost:8081/api` - Serveur de développement local
- `http://********:8081/api` - Émulateur Android
- `http://YOUR_IP:8081/api` - Appareil physique

### 2. Structure des Endpoints

Tous les endpoints suivent la structure définie dans le backend Laravel :

```
/api
├── /login (POST)
├── /logout (POST)
├── /refresh (POST)
├── /user (GET)
├── /time-entries
│   ├── /current (GET)
│   ├── /clock-in (POST)
│   ├── /clock-out (POST)
│   └── /history (GET)
├── /worksites
│   ├── /nearby (GET)
│   └── / (GET, POST, PUT, DELETE)
├── /location
│   ├── /check (POST)
│   ├── /verify (POST)
│   └── /log-verification (POST)
├── /security
│   ├── /log-violation (POST)
│   └── /log-clock-event (POST)
└── /notifications
    ├── /request-verification (POST)
    ├── /assign-site (POST)
    └── /update-fcm-token (POST)
```

## Services API

### 1. ApiService (Core)

Service principal pour toutes les requêtes HTTP :

```dart
final apiService = ApiService();
apiService.initialize();

// GET request
final response = await apiService.get('/endpoint');

// POST request
final response = await apiService.post('/endpoint', data);
```

### 2. AuthService

Gestion de l'authentification :

```dart
final authService = AuthService();
final result = await authService.login(email, password);

if (result.isSuccess) {
  // Connexion réussie
  final user = result.user;
}
```

### 3. TimeTrackingService

Gestion du suivi du temps :

```dart
final timeService = TimeTrackingService();

// Clock in
final result = await timeService.clockIn(worksite, user);

// Clock out
final result = await timeService.clockOut();

// Get history
final entries = await timeService.getTimeEntryHistory();
```

### 4. WorksiteService

Gestion des sites de travail :

```dart
final worksiteService = WorksiteService();

// Get nearby worksites
final worksites = await worksiteService.getNearbyWorksites();

// Check location
final result = await worksiteService.checkLocation(
  worksiteId: 'uuid',
  latitude: 40.7128,
  longitude: -74.0060,
);
```

## Test de Connexion API

### 1. Interface de Test

L'application inclut un écran de test API accessible depuis l'écran de connexion :

1. Ouvrez l'application
2. Sur l'écran de connexion, cliquez sur "Test API Connection"
3. Utilisez les boutons de test pour vérifier la connectivité

### 2. Tests Disponibles

- **Test API Health** : Vérifie si l'API répond
- **Test Public Endpoints** : Teste les endpoints publics
- **Run All Tests** : Exécute tous les tests disponibles

### 3. Service de Test Programmatique

```dart
final apiTestService = ApiTestService();

// Test simple
final isHealthy = await apiTestService.testApiHealth();

// Tests complets
await apiTestService.runComprehensiveTests();
```

## Format des Réponses

Toutes les réponses de l'API suivent ce format standard :

```json
{
  "success": true|false,
  "message": "Message de réponse",
  "data": {...}|[...],
  "errors": {...} // Seulement en cas d'erreurs de validation
}
```

## Gestion des Erreurs

### 1. Codes d'Erreur HTTP

- `400` - Bad Request (erreurs de validation)
- `401` - Unauthorized (token invalide/expiré)
- `403` - Forbidden (permissions insuffisantes)
- `404` - Not Found
- `422` - Unprocessable Entity (erreurs de validation)
- `500` - Internal Server Error

### 2. Gestion dans l'App

```dart
try {
  final response = await apiService.get('/endpoint');
  // Traiter la réponse
} on ApiException catch (e) {
  // Gérer l'erreur API
  print('Erreur API: ${e.message} (Code: ${e.statusCode})');
} catch (e) {
  // Gérer les autres erreurs
  print('Erreur: $e');
}
```

## Authentification

### 1. Token JWT

L'authentification utilise des tokens JWT :

```dart
// Le token est automatiquement ajouté aux headers
Authorization: Bearer {token}
```

### 2. Refresh Token

Le token est automatiquement rafraîchi en cas d'expiration :

```dart
// Géré automatiquement par ApiService
// En cas d'erreur 401, tentative de refresh
```

## Sécurité

### 1. Validation des Données

Toutes les données sont validées côté client et serveur :

```dart
// Validation email
if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
  return 'Email invalide';
}
```

### 2. Chiffrement

- Les mots de passe sont envoyés en clair (HTTPS requis)
- Le backend gère le hachage sécurisé
- Les tokens JWT sont signés

## Débogage

### 1. Logs de Développement

En mode debug, tous les appels API sont loggés :

```dart
// Activé automatiquement en mode debug
LogInterceptor(
  requestBody: kDebugMode,
  responseBody: kDebugMode,
)
```

### 2. Vérification de Connectivité

```bash
# Tester l'API manuellement
curl http://localhost:8081/api/health

# Réponse attendue
{
  "success": true,
  "message": "API is running",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

## Dépannage

### 1. Problèmes Courants

**API non accessible :**
- Vérifier que le serveur backend est démarré
- Vérifier l'URL dans `app_constants.dart`
- Vérifier les paramètres réseau/firewall

**Erreurs d'authentification :**
- Vérifier les credentials
- Vérifier l'expiration du token
- Vérifier les permissions utilisateur

**Erreurs de validation :**
- Vérifier le format des données envoyées
- Consulter les logs backend pour plus de détails

### 2. Outils de Debug

- **Flutter Inspector** : Pour l'état de l'application
- **Network Inspector** : Pour les requêtes HTTP
- **Console Logs** : Pour les erreurs détaillées
- **API Test Screen** : Pour tester la connectivité

## Prochaines Étapes

1. **Tester la connexion** avec l'écran de test API
2. **Configurer l'authentification** avec des vrais utilisateurs
3. **Implémenter les fonctionnalités** de suivi du temps
4. **Tester sur différents appareils** (émulateur, physique)
5. **Optimiser les performances** et la gestion d'erreurs

## Support

Pour toute question ou problème :
1. Consulter les logs de l'application
2. Utiliser l'écran de test API
3. Vérifier la documentation backend
4. Consulter les exemples de code dans les services
