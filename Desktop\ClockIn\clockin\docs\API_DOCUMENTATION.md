# ClockIn API Documentation

## Base URL
```
http://localhost/clockin-api/public/api
```

## Authentication
All API endpoints (except login) require a Bearer token in the Authorization header:
```
Authorization: Bearer {token}
```

## Response Format
All responses follow this format:
```json
{
    "success": true|false,
    "message": "Response message",
    "data": {...}|[...],
    "errors": {...} // Only present on validation errors
}
```

## Endpoints

### Authentication

#### POST /login
Authenticate user and return JWT token.

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "device_info": {
        "platform": "flutter",
        "timestamp": "2024-01-01T00:00:00Z"
    }
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "token": "jwt_token_here",
        "user": {
            "id": "uuid",
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "employee",
            "created_at": "2024-01-01T00:00:00Z"
        }
    }
}
```

#### POST /logout
Invalidate current token.

**Response:**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

#### POST /refresh
Refresh JWT token.

**Response:**
```json
{
    "success": true,
    "data": {
        "token": "new_jwt_token_here"
    }
}
```

### Time Tracking

#### GET /time-entries/current
Get current active time entry for authenticated user.

**Response:**
```json
{
    "success": true,
    "data": {
        "id": "uuid",
        "user_id": "uuid",
        "worksite_id": "uuid",
        "clock_in_time": "2024-01-01T08:00:00Z",
        "clock_in_location": {
            "latitude": 40.7128,
            "longitude": -74.0060,
            "accuracy": 5.0,
            "timestamp": "2024-01-01T08:00:00Z",
            "address": "123 Main St, New York, NY"
        },
        "status": "active"
    }
}
```

#### POST /time-entries/clock-in
Start a new time entry.

**Request:**
```json
{
    "user_id": "uuid",
    "worksite_id": "uuid",
    "clock_in_time": "2024-01-01T08:00:00Z",
    "clock_in_location": {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "accuracy": 5.0,
        "timestamp": "2024-01-01T08:00:00Z",
        "address": "123 Main St, New York, NY"
    },
    "device_fingerprint": "device_id_hash",
    "security_info": {
        "device_id": "device123",
        "is_physical_device": true,
        "is_mock_location_enabled": false
    }
}
```

#### POST /time-entries/clock-out
End current time entry.

**Request:**
```json
{
    "entry_id": "uuid",
    "clock_out_time": "2024-01-01T17:00:00Z",
    "clock_out_location": {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "accuracy": 5.0,
        "timestamp": "2024-01-01T17:00:00Z",
        "address": "123 Main St, New York, NY"
    },
    "total_duration": 32400
}
```

#### GET /time-entries/history
Get time entry history with optional filters.

**Query Parameters:**
- `start_date`: ISO date string
- `end_date`: ISO date string
- `limit`: Number of entries to return

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": "uuid",
            "user_id": "uuid",
            "worksite_id": "uuid",
            "clock_in_time": "2024-01-01T08:00:00Z",
            "clock_out_time": "2024-01-01T17:00:00Z",
            "total_duration": 32400,
            "status": "completed"
        }
    ]
}
```

### Worksites

#### GET /worksites/nearby
Get worksites near current user location.

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": "uuid",
            "name": "Construction Site A",
            "description": "Main construction project",
            "location": {
                "latitude": 40.7128,
                "longitude": -74.0060
            },
            "radius": 50.0,
            "address": "123 Construction Ave, NY",
            "is_active": true
        }
    ]
}
```

#### POST /worksites
Create new worksite (Admin only).

**Request:**
```json
{
    "name": "New Construction Site",
    "description": "Description of the site",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "radius": 50.0,
    "address": "123 New Site St, NY",
    "assigned_employees": ["uuid1", "uuid2"]
}
```

### Location Verification

#### POST /check-location
Check if user is within worksite radius.

**Request:**
```json
{
    "worksite_id": "uuid",
    "latitude": 40.7128,
    "longitude": -74.0060
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "within_radius": true,
        "distance": 25.5,
        "worksite": {
            "id": "uuid",
            "name": "Construction Site A",
            "radius": 50.0
        }
    }
}
```

#### POST /verify-location
Submit location verification response.

**Request:**
```json
{
    "verification_id": "uuid",
    "location": {
        "latitude": 40.7128,
        "longitude": -74.0060,
        "accuracy": 5.0,
        "timestamp": "2024-01-01T12:00:00Z"
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### Security & Logging

#### POST /log-security-violation
Log security violation.

**Request:**
```json
{
    "user_id": "uuid",
    "worksite_id": "uuid",
    "violation": "Mock location detected",
    "timestamp": "2024-01-01T12:00:00Z",
    "location": {
        "latitude": 40.7128,
        "longitude": -74.0060
    }
}
```

#### POST /log-clock-event
Log clock in/out events.

**Request:**
```json
{
    "user_id": "uuid",
    "worksite_id": "uuid",
    "event_type": "clock_in",
    "status": "success",
    "details": "Optional details",
    "timestamp": "2024-01-01T08:00:00Z"
}
```

### Employee Management (Admin)

#### GET /employees
Get list of employees.

**Query Parameters:**
- `search`: Search term
- `page`: Page number
- `per_page`: Items per page

#### POST /employees
Create new employee.

**Request:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "employee",
    "phone_number": "+1234567890",
    "department": "Construction"
}
```

#### GET /employees/{id}
Get employee details.

#### PUT /employees/{id}
Update employee.

#### DELETE /employees/{id}
Delete employee.

### Notifications

#### POST /request-verification
Request location verification from employee (Admin).

**Request:**
```json
{
    "user_id": "uuid",
    "message": "Please verify your current location"
}
```

#### POST /assign-site
Assign worksite to employees and send notification.

**Request:**
```json
{
    "worksite_id": "uuid",
    "employee_ids": ["uuid1", "uuid2"],
    "message": "You have been assigned to a new worksite"
}
```

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/expired token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('employee', 'admin', 'supervisor') DEFAULT 'employee',
    phone_number VARCHAR(20),
    department VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Worksites Table
```sql
CREATE TABLE worksites (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    radius DECIMAL(8, 2) DEFAULT 50.00,
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### Time Entries Table
```sql
CREATE TABLE time_entries (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    worksite_id VARCHAR(36) NOT NULL,
    clock_in_time TIMESTAMP NOT NULL,
    clock_out_time TIMESTAMP NULL,
    total_duration INT NULL,
    clock_in_latitude DECIMAL(10, 8),
    clock_in_longitude DECIMAL(11, 8),
    clock_out_latitude DECIMAL(10, 8),
    clock_out_longitude DECIMAL(11, 8),
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (worksite_id) REFERENCES worksites(id)
);
```

### Security Logs Table
```sql
CREATE TABLE security_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36),
    worksite_id VARCHAR(36),
    violation_type VARCHAR(100),
    details TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (worksite_id) REFERENCES worksites(id)
);
```
