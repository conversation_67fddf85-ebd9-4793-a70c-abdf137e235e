import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/theme/app_theme.dart';
import 'core/localization/app_localizations.dart';
import 'core/models/user_model.dart';
import 'core/models/worksite_model.dart';
import 'core/models/time_entry_model.dart';
import 'features/auth/presentation/screens/login_screen.dart';
import 'features/employee/presentation/screens/employee_dashboard_screen.dart';
import 'features/auth/presentation/providers/auth_provider.dart';
import 'features/auth/presentation/providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(UserModelAdapter());
  Hive.registerAdapter(UserRoleAdapter());
  Hive.registerAdapter(TimeEntryModelAdapter());
  Hive.registerAdapter(LocationDataAdapter());
  Hive.registerAdapter(TimeEntryStatusAdapter());
  Hive.registerAdapter(WorksiteModelAdapter());
  Hive.registerAdapter(WorksiteTypeAdapter());

  runApp(
    const ProviderScope(
      child: ClockInApp(),
    ),
  );
}

class ClockInApp extends ConsumerWidget {
  const ClockInApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final languageState = ref.watch(languageProvider);
    final authState = ref.watch(authProvider);

    return MaterialApp(
      title: 'ClockIn',
      debugShowCheckedModeBanner: false,

      // Theme
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,

      // Localization
      locale: languageState.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,

      // Routing
      home: _getHomeScreen(authState),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/dashboard': (context) => const EmployeeDashboardScreen(),
      },

      // Builder for RTL support
      builder: (context, child) {
        return Directionality(
          textDirection: languageState.textDirection,
          child: child!,
        );
      },
    );
  }

  Widget _getHomeScreen(AuthState authState) {
    if (authState.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (authState.isAuthenticated && authState.user != null) {
      // Route based on user role
      switch (authState.user!.role) {
        case UserRole.employee:
          return const EmployeeDashboardScreen();
        case UserRole.admin:
        case UserRole.supervisor:
          // TODO: Implement admin dashboard
          return const EmployeeDashboardScreen(); // Temporary
      }
    }

    return const LoginScreen();
  }
}