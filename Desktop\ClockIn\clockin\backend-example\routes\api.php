<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TimeEntryController;
use App\Http\Controllers\WorksiteController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\SecurityController;
use App\Http\Controllers\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes (no authentication required)
Route::post('/login', [AuthController::class, 'login']);

// Protected routes (authentication required)
Route::middleware('auth:api')->group(function () {
    
    // Authentication
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/me', [AuthController::class, 'me']);
    
    // Time Tracking
    Route::prefix('time-entries')->group(function () {
        Route::get('/current', [TimeEntryController::class, 'getCurrentEntry']);
        Route::post('/clock-in', [TimeEntryController::class, 'clockIn']);
        Route::post('/clock-out', [TimeEntryController::class, 'clockOut']);
        Route::get('/history', [TimeEntryController::class, 'getHistory']);
        Route::get('/stats/today', [TimeEntryController::class, 'getTodayStats']);
        Route::get('/stats/week', [TimeEntryController::class, 'getWeekStats']);
        Route::get('/stats/month', [TimeEntryController::class, 'getMonthStats']);
    });
    
    // Worksites
    Route::prefix('worksites')->group(function () {
        Route::get('/nearby', [WorksiteController::class, 'getNearby']);
        Route::get('/', [WorksiteController::class, 'index']);
        Route::get('/{id}', [WorksiteController::class, 'show']);
        
        // Admin only routes
        Route::middleware('role:admin')->group(function () {
            Route::post('/', [WorksiteController::class, 'store']);
            Route::put('/{id}', [WorksiteController::class, 'update']);
            Route::delete('/{id}', [WorksiteController::class, 'destroy']);
            Route::post('/assign-employees', [WorksiteController::class, 'assignEmployees']);
        });
    });
    
    // Location Services
    Route::prefix('location')->group(function () {
        Route::post('/check', [LocationController::class, 'checkLocation']);
        Route::post('/verify', [LocationController::class, 'verifyLocation']);
        Route::post('/log-verification', [LocationController::class, 'logVerification']);
    });
    
    // Security & Logging
    Route::prefix('security')->group(function () {
        Route::post('/log-violation', [SecurityController::class, 'logViolation']);
        Route::post('/log-clock-event', [SecurityController::class, 'logClockEvent']);
        Route::get('/violations', [SecurityController::class, 'getViolations']);
        Route::get('/device-info', [SecurityController::class, 'getDeviceInfo']);
    });
    
    // Employee Management (Admin only)
    Route::middleware('role:admin')->prefix('employees')->group(function () {
        Route::get('/', [EmployeeController::class, 'index']);
        Route::post('/', [EmployeeController::class, 'store']);
        Route::get('/{id}', [EmployeeController::class, 'show']);
        Route::put('/{id}', [EmployeeController::class, 'update']);
        Route::delete('/{id}', [EmployeeController::class, 'destroy']);
        Route::post('/{id}/activate', [EmployeeController::class, 'activate']);
        Route::post('/{id}/deactivate', [EmployeeController::class, 'deactivate']);
    });
    
    // Notifications
    Route::prefix('notifications')->group(function () {
        Route::post('/request-verification', [NotificationController::class, 'requestVerification']);
        Route::post('/assign-site', [NotificationController::class, 'assignSite']);
        Route::post('/update-fcm-token', [NotificationController::class, 'updateFcmToken']);
        Route::get('/history', [NotificationController::class, 'getHistory']);
    });
    
    // Admin Dashboard
    Route::middleware('role:admin')->prefix('admin')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard']);
        Route::get('/reports/daily', [AdminController::class, 'dailyReport']);
        Route::get('/reports/weekly', [AdminController::class, 'weeklyReport']);
        Route::get('/reports/monthly', [AdminController::class, 'monthlyReport']);
        Route::post('/reports/export', [AdminController::class, 'exportReport']);
    });
    
    // File uploads
    Route::post('/upload/profile-image', [FileController::class, 'uploadProfileImage']);
    Route::post('/upload/document', [FileController::class, 'uploadDocument']);
});

// Webhook routes (for external services)
Route::prefix('webhooks')->group(function () {
    Route::post('/firebase', [WebhookController::class, 'firebase']);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is running',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});

// Fallback route
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'Endpoint not found',
        'error' => 'The requested endpoint does not exist'
    ], 404);
});
