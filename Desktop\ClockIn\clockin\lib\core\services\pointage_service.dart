import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/worksite_model.dart';
import 'api_service.dart';

class PointageService {
  static final PointageService _instance = PointageService._internal();
  factory PointageService() => _instance;
  PointageService._internal();

  final ApiService _apiService = ApiService();

  /// Check if user location is within site range
  Future<LocationCheckResult> checkLocation({
    required String siteId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final data = {
        'site_id': siteId,
        'latitude': latitude,
        'longitude': longitude,
      };

      final response = await _apiService.post('/check-location', data);

      if (response['success'] == true) {
        final responseData = response['data'];
        return LocationCheckResult(
          isWithinRange: responseData['is_within_range'] ?? false,
          site: responseData['site'] != null 
              ? SiteInfo.fromJson(responseData['site'])
              : null,
        );
      }

      return LocationCheckResult(
        isWithinRange: false,
        site: null,
      );
    } catch (e) {
      debugPrint('Failed to check location: $e');
      return LocationCheckResult(
        isWithinRange: false,
        site: null,
      );
    }
  }

  /// Save pointage (clock in/out)
  Future<PointageResult> savePointage({
    required String siteId,
    required double latitude,
    required double longitude,
    required String type, // 'entree' or 'sortie'
    String? notes,
  }) async {
    try {
      final data = {
        'site_id': siteId,
        'latitude': latitude,
        'longitude': longitude,
        'type': type,
        if (notes != null) 'notes': notes,
      };

      final response = await _apiService.post('/save-pointage', data);

      if (response['success'] == true) {
        return PointageResult(
          success: true,
          message: _extractMessage(response['message']),
          pointage: response['data'] != null 
              ? PointageData.fromJson(response['data'])
              : null,
        );
      }

      return PointageResult(
        success: false,
        message: _extractMessage(response['message']) ?? 'Pointage failed',
        pointage: null,
      );
    } catch (e) {
      debugPrint('Failed to save pointage: $e');
      return PointageResult(
        success: false,
        message: 'Error saving pointage: $e',
        pointage: null,
      );
    }
  }

  /// Get user's assigned sites
  Future<List<WorksiteModel>> getMySites() async {
    try {
      final response = await _apiService.get('/my-sites');

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => WorksiteModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get my sites: $e');
      return [];
    }
  }

  /// Get pointages (admin only)
  Future<List<PointageData>> getPointages({
    String? userId,
    String? siteId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (userId != null) queryParams['user_id'] = userId;
      if (siteId != null) queryParams['site_id'] = siteId;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _apiService.get('/pointages', queryParameters: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => PointageData.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get pointages: $e');
      return [];
    }
  }

  /// Extract message from multilingual response
  String? _extractMessage(dynamic message) {
    if (message is String) return message;
    if (message is Map<String, dynamic>) {
      // Try to get message in preferred language order: fr, en, ar
      return message['fr'] ?? message['en'] ?? message['ar'];
    }
    return null;
  }
}

class LocationCheckResult {
  final bool isWithinRange;
  final SiteInfo? site;

  LocationCheckResult({
    required this.isWithinRange,
    this.site,
  });
}

class SiteInfo {
  final String id;
  final String name;

  SiteInfo({
    required this.id,
    required this.name,
  });

  factory SiteInfo.fromJson(Map<String, dynamic> json) {
    return SiteInfo(
      id: json['id'].toString(),
      name: json['name'] ?? '',
    );
  }
}

class PointageResult {
  final bool success;
  final String message;
  final PointageData? pointage;

  PointageResult({
    required this.success,
    required this.message,
    this.pointage,
  });
}

class PointageData {
  final String id;
  final String userId;
  final String siteId;
  final String type;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final String? notes;

  PointageData({
    required this.id,
    required this.userId,
    required this.siteId,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    this.notes,
  });

  factory PointageData.fromJson(Map<String, dynamic> json) {
    return PointageData(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      siteId: json['site_id'].toString(),
      type: json['type'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'site_id': siteId,
      'type': type,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
    };
  }
}
