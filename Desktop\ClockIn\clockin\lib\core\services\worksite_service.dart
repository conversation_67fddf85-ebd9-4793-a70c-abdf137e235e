import 'package:flutter/foundation.dart';
import '../models/worksite_model.dart';
import 'api_service.dart';

class WorksiteService {
  static final WorksiteService _instance = WorksiteService._internal();
  factory WorksiteService() => _instance;
  WorksiteService._internal();

  final ApiService _apiService = ApiService();
  List<WorksiteModel> _nearbyWorksites = [];
  WorksiteModel? _currentWorksite;

  List<WorksiteModel> get nearbyWorksites => _nearbyWorksites;
  WorksiteModel? get currentWorksite => _currentWorksite;

  /// Get user's assigned sites
  Future<List<WorksiteModel>> getMySites() async {
    try {
      final response = await _apiService.get('/my-sites');

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        _nearbyWorksites = data.map((json) => WorksiteModel.fromJson(json)).toList();
        return _nearbyWorksites;
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get my sites: $e');
      return [];
    }
  }

  /// Get all sites (admin only)
  Future<List<WorksiteModel>> getAllSites({
    int? page,
    int? perPage,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiService.get(
        '/sites',
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => WorksiteModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Failed to get all sites: $e');
      return [];
    }
  }

  /// Get worksite by ID
  Future<WorksiteModel?> getWorksiteById(String id) async {
    try {
      final response = await _apiService.get('/worksites/$id');

      if (response['success'] == true) {
        return WorksiteModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get worksite by ID: $e');
      return null;
    }
  }

  /// Create new site (admin only)
  Future<WorksiteModel?> createSite({
    required String name,
    required String description,
    required double latitude,
    required double longitude,
    required double radius,
    required String address,
    List<String>? assignedEmployees,
  }) async {
    try {
      final data = {
        'name': name,
        'description': description,
        'latitude': latitude,
        'longitude': longitude,
        'radius': radius,
        'address': address,
        if (assignedEmployees != null) 'assigned_employees': assignedEmployees,
      };

      final response = await _apiService.post('/sites', data);

      if (response['success'] == true) {
        return WorksiteModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to create site: $e');
      return null;
    }
  }

  /// Update worksite (admin only)
  Future<WorksiteModel?> updateWorksite(
    String id, {
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    double? radius,
    String? address,
    bool? isActive,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (latitude != null) data['latitude'] = latitude;
      if (longitude != null) data['longitude'] = longitude;
      if (radius != null) data['radius'] = radius;
      if (address != null) data['address'] = address;
      if (isActive != null) data['is_active'] = isActive;

      final response = await _apiService.put('/worksites/$id', data);

      if (response['success'] == true) {
        return WorksiteModel.fromJson(response['data']);
      }

      return null;
    } catch (e) {
      debugPrint('Failed to update worksite: $e');
      return null;
    }
  }

  /// Delete worksite (admin only)
  Future<bool> deleteWorksite(String id) async {
    try {
      final response = await _apiService.delete('/worksites/$id');
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to delete worksite: $e');
      return false;
    }
  }

  /// Check if user is within site radius
  Future<LocationCheckResult> checkLocation({
    required String siteId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final data = {
        'site_id': siteId,
        'latitude': latitude,
        'longitude': longitude,
      };

      final response = await _apiService.post('/check-location', data);

      if (response['success'] == true) {
        final responseData = response['data'];
        return LocationCheckResult(
          withinRadius: responseData['is_within_range'] ?? false,
          distance: 0.0, // Backend doesn't return distance
          worksite: responseData['site'] != null
              ? WorksiteModel.fromJson(responseData['site'])
              : null,
        );
      }

      return LocationCheckResult(
        withinRadius: false,
        distance: double.infinity,
        worksite: null,
      );
    } catch (e) {
      debugPrint('Failed to check location: $e');
      return LocationCheckResult(
        withinRadius: false,
        distance: double.infinity,
        worksite: null,
      );
    }
  }

  /// Assign site to employees (admin only)
  Future<bool> assignSite({
    required String siteId,
    required List<String> employeeIds,
    String? message,
  }) async {
    try {
      final data = {
        'site_id': siteId,
        'employee_ids': employeeIds,
        if (message != null) 'message': message,
      };

      final response = await _apiService.post('/assign-site', data);
      return response['success'] == true;
    } catch (e) {
      debugPrint('Failed to assign site: $e');
      return false;
    }
  }

  /// Set current worksite
  void setCurrentWorksite(WorksiteModel? worksite) {
    _currentWorksite = worksite;
  }

  /// Clear cached data
  void clearCache() {
    _nearbyWorksites.clear();
    _currentWorksite = null;
  }
}

class LocationCheckResult {
  final bool withinRadius;
  final double distance;
  final WorksiteModel? worksite;

  LocationCheckResult({
    required this.withinRadius,
    required this.distance,
    this.worksite,
  });
}
